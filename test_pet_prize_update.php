<?php
/**
 * 测试pet_prize表更新功能
 * 用于验证当store_id=2且有voucher_code时的异步处理逻辑
 */

require_once 'framework/function/global.func.php';
require_once 'framework/db/db.factory.php';

use Upet\Models\Eshop\PetPrize;
use Upet\Modules\Order\Queues\UpdatePetPrizeStatusQueue;

// 测试PetPrize模型的基本功能
function testPetPrizeModel() {
    echo "=== 测试PetPrize模型 ===\n";
    
    $testCouponCode = 'TEST_COUPON_' . time();
    
    // 测试检查是否存在
    $exists = PetPrize::existsByCouponCode($testCouponCode);
    echo "测试优惠券码是否存在: " . ($exists ? '存在' : '不存在') . "\n";
    
    // 如果存在测试记录，测试更新功能
    if ($exists) {
        $result = PetPrize::updateReceiveStatus($testCouponCode, 3);
        echo "更新核销状态结果: " . ($result ? '成功' : '失败') . "\n";
        
        $record = PetPrize::getByCouponCode($testCouponCode);
        echo "更新后的记录: " . json_encode($record) . "\n";
    }
    
    echo "\n";
}

// 测试异步队列
function testUpdateQueue() {
    echo "=== 测试异步队列 ===\n";
    
    $testCouponCode = 'TEST_QUEUE_' . time();
    
    try {
        // 创建队列任务
        UpdatePetPrizeStatusQueue::dispatch($testCouponCode);
        echo "异步队列任务创建成功\n";
    } catch (Exception $e) {
        echo "异步队列任务创建失败: " . $e->getMessage() . "\n";
    }
    
    echo "\n";
}

// 模拟datacenter.logic.php中的逻辑
function testDatacenterLogic() {
    echo "=== 测试datacenter逻辑 ===\n";
    
    // 模拟订单数据
    $order = ['store_id' => 2];
    $order_common = ['voucher_code' => 'TEST_DATACENTER_' . time()];
    
    // 模拟datacenter.logic.php中的条件判断
    if ($order['store_id'] == 2 && !empty($order_common['voucher_code'])) {
        echo "条件满足: store_id=2 且 voucher_code不为空\n";
        echo "voucher_code: " . $order_common['voucher_code'] . "\n";
        
        try {
            UpdatePetPrizeStatusQueue::dispatch($order_common['voucher_code']);
            echo "异步任务派发成功\n";
        } catch (Exception $e) {
            echo "异步任务派发失败: " . $e->getMessage() . "\n";
        }
    } else {
        echo "条件不满足，不会触发异步任务\n";
    }
    
    echo "\n";
}

// 运行测试
echo "开始测试pet_prize更新功能\n";
echo "================================\n\n";

testPetPrizeModel();
testUpdateQueue();
testDatacenterLogic();

echo "测试完成\n";
?>
