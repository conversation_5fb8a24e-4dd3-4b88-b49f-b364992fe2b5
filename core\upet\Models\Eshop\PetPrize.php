<?php

namespace Upet\Models\Eshop;

use Upet\Models\Model;

class PetPrize extends Model
{
    protected $pk = 'id';
    protected $table = 'eshop.pet_prize';

    /**
     * 根据coupon_code查询记录
     * @param string $couponCode
     * @return array
     */
    public static function getByCouponCode($couponCode)
    {
        $result = self::where('coupon_code', $couponCode)->find();
        return $result ? $result->toArray() : [];
    }

    /**
     * 更新核销状态
     * @param string $couponCode
     * @param int $receiveStatus
     * @return bool
     */
    public static function updateReceiveStatus($couponCode, $receiveStatus)
    {
        return self::where('coupon_code', $couponCode)->update(['receive_status' => $receiveStatus]);
    }

    /**
     * 检查是否存在指定coupon_code的记录
     * @param string $couponCode
     * @return bool
     */
    public static function existsByCouponCode($couponCode)
    {
        return self::where('coupon_code', $couponCode)->count() > 0;
    }
}
