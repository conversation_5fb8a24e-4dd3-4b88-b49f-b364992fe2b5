<?php

/**
 * 数据中心业务处理类
 * <AUTHOR>
 * @date 2021.03.15
 */

use Upet\Integrates\Redis\RedisManager;
use Upet\Models\Datacenter\MemberPropertyGuaranteeQuota;
use Upet\Models\DcOrder\IntegralExchange;
use Upet\Models\DcProduct\Sku;
use Upet\Models\Goods;
use Upet\Models\Order as OrderAlias;
use Upet\Models\OrdersSalesperson;
use Upet\Modules\Order\Actions\VipOrderPaidAction;
use Upet\Modules\Member\Queues\SyncOrdersSalespersonQueue;
use Upet\Modules\Member\Queues\SyncDisDistributorFansOrder;
use Upet\Modules\Order\Queues\UpdatePetPrizeStatusQueue;

defined('InShopNC') or exit('Access Invalid!');

class datacenterLogic
{
    /**
     * 通知数据中心电商订单已支付
     * @param $order_info
     * @param $update_info
     * @param int $type 1 实物 2 虚拟 3 保险订单 4 积分
     * @param string $phone 手机号
     * @return bool
     * @throws Exception
     */
    public function syncDatacenterOrderPay($order_info, $update_info, $type = 1, $phone = "")
    {
        $param = array();
        $param['addTime'] = date('Y-m-d H:i:s');
        $param['clientIP'] = getClientIp();
        $param['discount'] = "0";
        $param['orderTime'] = date('Y-m-d H:i:s', $order_info['add_time']);
        $param['outTradeNo'] = $order_info['order_sn'];
        $param['payPrice'] = (string)($order_info['order_amount'] * 100);
        $param['payTime'] = date('Y-m-d H:i:s', $update_info['payment_time']);
        $param['tradeNo'] = (string)$update_info['trade_no'];
        $param['orderId'] = $order_info['order_sn'];
        $param['payType'] = ($update_info['payment_code'] == 'card' ? '8' : ($update_info['payment_code'] == 'ali_native' ? '11' : '1'));

        $sign = Logic('dianyin_pay')->getSignature($param);
        $param['sign'] = $sign;
        $jsonstr = json_encode($param);
        $url = "/order-api/order/offlinenotify";
        list($code, $response) = http_post_json(C('datacenter_orderpay_url') . $url, $jsonstr, $phone, $order_info['order_sn'], $type);
        $result = json_decode($response, true);
        if ($result['result'] == "success" && $code == '200') {
            return true;
        } else {
            return false;
        }
    }

    /**
     * 同步数据中心拆单数据到电商库
     */
    public function syncDatacenterOrderInfo($order_data)
    {
        if (!is_array($order_data) && empty($order_data)) {
            throw new Exception('订单数据为空');
        }
        $power_model = Model('power');
        $model_order = Model('order');
        $model_vr_order = Model('vr_order');
        $model_goods = Model('goods');
        $model_gift_order = Model('gift_order');
        $model_goods->beginTransaction();
        try {
            $queriedOrders = [];

            // 倒减合计
            $historyCount = [
                'seller_discount_price' => 0
            ];

            foreach ($order_data as $key => $data) {
                $order = array();
                $order_goods = array();
                $order_info = $data['order_info'];
                $goods_list_data = $data['order_goods']; //todo

                if ($order_info['is_virtual'] == 1) { // 虚拟订单
                    if ($order_info['order_type'] == 7) { //保险订单
                        $field = "order_id,store_id,buyer_id,buyer_phone,order_id_from,vr_invalid_refund,vr_indate,order_amount,goods_num,erp_order_sn";
                        $gift_order_info = $model_gift_order->getOrderInfo(['order_sn' => $order_info['old_order_sn']], $field, true);
                        if (is_array($gift_order_info) && !empty($gift_order_info)) {
                            if ($gift_order_info['erp_order_sn']) {
                                continue;
                            }
                            $updata = array();
                            $updata['erp_order_sn'] = $order_info['order_sn'];
                            $updata['oc_id'] = $goods_list_data[0]['oc_id'];
                            $update_vrorder_result = $model_vr_order->editOrder($updata, array('order_id' => $gift_order_info['order_id']));
                            if (!$update_vrorder_result) {
                                throw new Exception('更新虚拟保险订单失败');
                            }
                            $order['store_id'] = $gift_order_info['store_id'];
                            $order['order_id'] = $gift_order_info['order_id'];
                            $order['buyer_id'] = $gift_order_info['buyer_id'];
                            $order['order_amount'] = isset($order_info['order_amount']) ? floatval($order_info['order_amount']) / 100 : 0;
                            $order['goods_num'] = $gift_order_info['goods_num'];
                            $order['buyer_phone'] = $gift_order_info['buyer_phone'];
                            $parent_order_info = $model_vr_order->getOrderInfo(['order_id' => $gift_order_info['order_id_from']], "dis_type,dis_member_id,out_member_id,dis_commis_rate,is_dis", true);
                            $order['dis_type'] = $parent_order_info['dis_type'];
                            $order['out_member_id'] = $parent_order_info['out_member_id'];
                            $order['dis_member_id'] = $parent_order_info['dis_member_id'];
                            $order['dis_commis_rate'] = $parent_order_info['dis_commis_rate'];
                            $order['is_dis'] = $parent_order_info['is_dis'];
                            $order['vr_invalid_refund'] = $gift_order_info['vr_invalid_refund'];
                            $order['vr_indate'] = $gift_order_info['vr_indate'];
                            $insert = $model_gift_order->addDatacenterOrderCode($order, $data['order_codeinfo']);
                            if (!$insert) {
                                throw new Exception('保险订单兑换码生成失败');
                            }
                        }
                    } elseif ($order_info['order_type'] == 8) { //积分订单
                        $exchange = IntegralExchange::where('order_sn', $order_info['old_order_sn'])
                            ->field('id,erp_order_sn,type,vr_order_id,erp_mobile,gtype')
                            ->find();

                        if (empty($exchange) || $exchange['erp_order_sn']) {
                            continue;
                        }

                        // 虚拟商品订单 type=5爱心币无核销码不处理
                        if ($exchange['type'] == 2 && $exchange['gtype'] != 5) {
                            $field = "buyer_phone,encrypt_mobile,dis_type,out_member_id,dis_member_id,dis_commis_rate,is_dis,vr_invalid_refund,vr_indate,buyer_id,store_id,order_id,order_amount,goods_num,goods_name,order_sn";
                            $point_vrorder_info = $model_vr_order->getOrderInfo(['order_id' => $exchange['vr_order_id']], $field, true);
                            $updata = array();
                            $updata['erp_status'] = 1;
                            $updata['erp_order_status'] = 2; //ERP订单状态(1-未支付，2-已支付，3-已退款，4-已取消)
                            //$updata['erp_order_ids'] = serialize($orderadd);
                            $updata['erp_mobile'] = $exchange['erp_mobile'];
                            $updata['erp_time'] = time();
                            $updata['erp_order_sn'] = $order_info['order_sn'];
                            $updata['oc_id'] = $goods_list_data[0]['oc_id'];
                            $update_vrorder_result = $model_vr_order->editOrder($updata, array('order_id' => $point_vrorder_info['order_id']));
                            if (!$update_vrorder_result) {
                                throw new Exception('更新虚拟订单失败');
                            }
                            $order['order_sn'] = $point_vrorder_info['order_sn'];
                            $order['store_id'] = $point_vrorder_info['store_id'];
                            $order['order_id'] = $point_vrorder_info['order_id'];
                            $order['buyer_id'] = $point_vrorder_info['buyer_id'];
                            $order['order_amount'] = isset($point_vrorder_info['order_amount']) ? floatval($point_vrorder_info['order_amount']) : 0;
                            $order['goods_num'] = $point_vrorder_info['goods_num'];
                            $order['buyer_phone'] = $point_vrorder_info['buyer_phone'];
                            $order['encrypt_mobile'] = $point_vrorder_info['encrypt_mobile'];
                            $order['dis_type'] = $point_vrorder_info['dis_type'];
                            $order['out_member_id'] = $point_vrorder_info['out_member_id'];
                            $order['dis_member_id'] = $point_vrorder_info['dis_member_id'];
                            $order['dis_commis_rate'] = $point_vrorder_info['dis_commis_rate'];
                            $order['is_dis'] = $point_vrorder_info['is_dis'];
                            $order['vr_invalid_refund'] = $point_vrorder_info['vr_invalid_refund'];
                            $order['vr_indate'] = $point_vrorder_info['vr_indate'];
                            $order['goods_name'] = $point_vrorder_info['goods_name'];
                            $insert = $model_vr_order->addDatacenterOrderCode($order, $data['order_codeinfo']);
                            if (!$insert) {
                                throw new Exception('兑换码生成失败');
                            }

                            // 虚拟商品订单更新核销码信息
                            if (isset($data['order_codeinfo'][0]['vr_code'])) {
                                $exchange->coupon_code = $data['order_codeinfo'][0]['vr_code'];
                            }
                        }

                        $exchange->erp_order_sn = $order_info['order_sn'];
                        $exchange->save();
                    } elseif ($order_info['order_type'] == 17) { //会员卡订单
                        $orderadd = array();
                        if (empty($data['order_codeinfo'])) {
                            throw new Exception('核销码信息为空');
                        }
                        $vr_order_info = $model_vr_order->getOrderInfo(['order_sn' => $order_info['old_order_sn']], '*', true);

                        if (is_array($vr_order_info) && !empty($vr_order_info)) {
                            if ($key == 0 && $vr_order_info['erp_order_sn']) {
                                continue;
                            }
                            if ($key == 0) {
                                $updata = array();
                                $updata['erp_status'] = 1;
                                $updata['erp_order_status'] = 2; //ERP订单状态(1-未支付，2-已支付，3-已退款，4-已取消)
                                $updata['erp_order_ids'] = serialize($orderadd);
                                $updata['erp_mobile'] = $vr_order_info['buyer_phone'];
                                $updata['erp_time'] = time();
                                $updata['erp_order_sn'] = $order_info['order_sn'];
                                $updata['oc_id'] = $goods_list_data[0]['oc_id'];
                                $updata['goods_num'] = 1;
                                $updata['order_amount'] = floatval($order_info['order_amount']) / 100;
                                $update_vrorder_result = $model_vr_order->editOrder($updata, array('order_id' => $vr_order_info['order_id']));
                                if (!$update_vrorder_result) {
                                    throw new Exception('更新虚拟订单失败');
                                }
                                $newOrder = array_merge($vr_order_info, $updata);
                            } else {
                                $newOrder = $vr_order_info;
                                unset($newOrder['order_id']);
                                unset($newOrder['state_desc']);
                                unset($newOrder['payment_name']);
                                $newOrder['erp_order_sn'] = $order_info['order_sn'];
                                $newOrder['order_sn'] = $order_info['order_sn'];
                                $newOrder['oc_id'] = $goods_list_data[0]['oc_id'];
                                try {
                                    $newOrder['order_id'] = $model_vr_order->addOrder($newOrder);
                                } catch (Exception $e) {
                                    throw new Exception($e->getMessage());
                                }
                            }
                        }

                        $insert = $model_vr_order->addDatacenterOrderCode($newOrder, $data['order_codeinfo']);
                        if (!$insert) {
                            throw new Exception('兑换码生成失败');
                        }
                        //发送兑换码到手机
                        $param = array('order_id' => $order['order_id'], 'buyer_id' => $order['buyer_id'], 'buyer_phone' => $order['buyer_phone'], 'goods_name' => $order['goods_name']);
                        RealTimePush('sendVrCode', $param);
                    } else { //普通虚拟订单
                        $orderadd = array();
                        if (empty($data['order_codeinfo'])) {
                            throw new Exception('核销码信息为空');
                        }
                        $field = "order_sn,erp_order_sn,buyer_phone,encrypt_mobile,dis_type,out_member_id,dis_member_id,
                        dis_commis_rate,is_dis,vr_invalid_refund,vr_indate,buyer_id,store_id,order_id,order_amount,
                        goods_id,goods_name,order_promotion_type,goods_price,goods_num";
                        $vr_order_info = $model_vr_order->getOrderInfo(['order_sn' => $order_info['old_order_sn']], $field, true);

                        if (is_array($vr_order_info) && !empty($vr_order_info)) {
                            if ($vr_order_info['erp_order_sn']) {
                                continue;
                            }
                            $updata = array();
                            $updata['erp_status'] = 1;
                            $updata['erp_order_status'] = 2; //ERP订单状态(1-未支付，2-已支付，3-已退款，4-已取消)
                            $updata['erp_order_ids'] = serialize($orderadd);
                            $updata['erp_mobile'] = $vr_order_info['buyer_phone'];
                            $updata['erp_time'] = time();
                            $updata['erp_order_sn'] = $order_info['order_sn'];
                            $updata['oc_id'] = $goods_list_data[0]['oc_id'];
                            $update_vrorder_result = $model_vr_order->editOrder($updata, array('order_id' => $vr_order_info['order_id']));
                            if (!$update_vrorder_result) {
                                throw new Exception('更新虚拟订单失败');
                            }

                            if ($vr_order_info['order_promotion_type'] == 17) {
                                MemberPropertyGuaranteeQuota::freezeDecr([
                                    'order_sn' => $vr_order_info['order_sn'], // 支付单号
                                    'order_id' => 'v-' . $vr_order_info['order_id'], // v-23,r-23 子订单id，跳转详情使用
                                    'order_amount' => $vr_order_info['order_amount'], // 实付金额
                                    'goods_total' => $vr_order_info['goods_price'] * $vr_order_info['goods_num'], // 商品总金额
                                    'goods_name' => $vr_order_info['goods_name'], // 商品名称
                                    'goods_id' => $vr_order_info['goods_id'], // 商品id
                                    'buyer_id' => $vr_order_info['buyer_id'], // 用户id
                                ]);
                            }

                            $order['order_sn'] = $vr_order_info['order_sn'];
                            $order['store_id'] = $vr_order_info['store_id'];
                            $order['order_id'] = $vr_order_info['order_id'];
                            $order['buyer_id'] = $vr_order_info['buyer_id'];
                            $order['order_amount'] = isset($order_info['order_amount']) ? floatval($order_info['order_amount']) / 100 : 0;
                            $order['goods_num'] = $vr_order_info['goods_num'];
                            $order['buyer_phone'] = $vr_order_info['buyer_phone'];
                            $order['encrypt_mobile'] = $vr_order_info['encrypt_mobile'];
                            $order['dis_type'] = $vr_order_info['dis_type'];
                            $order['out_member_id'] = $vr_order_info['out_member_id'];
                            $order['dis_member_id'] = $vr_order_info['dis_member_id'];
                            $order['dis_commis_rate'] = $vr_order_info['dis_commis_rate'];
                            $order['is_dis'] = $vr_order_info['is_dis'];
                            $order['vr_invalid_refund'] = $vr_order_info['vr_invalid_refund'];
                            $order['vr_indate'] = $vr_order_info['vr_indate'];
                            $order['goods_name'] = $vr_order_info['goods_name'];
                            $insert = $model_vr_order->addDatacenterOrderCode($order, $data['order_codeinfo']);
                            if (!$insert) {
                                throw new Exception('兑换码生成失败');
                            }
                            //发送兑换码到手机
                            $param = array('order_id' => $order['order_id'], 'buyer_id' => $order['buyer_id'], 'buyer_phone' => $order['buyer_phone'], 'goods_name' => $order['goods_name']);
                            RealTimePush('sendVrCode', $param);
                        } else { //组合商品中的虚拟商品 todo  电商暂时没有这功能
                            $field = "order_id,store_id,store_name,buyer_id,buyer_name,buyer_phone,encrypt_mobile,order_from,order_type,chain_id,dis_type,payment_time,pay_sn,trade_no";
                            $real_order_info = $model_order->getOrderInfo(['order_sn' => $order_info['old_order_sn']], [], $field, '', '', true);
                            if (is_array($real_order_info) && !empty($real_order_info)) {
                                $order_common_info = $model_order->getOrderCommonInfo(array('order_id' => $real_order_info['order_id']));
                                $vr_goods_info = $goods_list_data[0];
                                $goods_info = $model_goods->getGoodsInfoByID($vr_goods_info['goods_id'], "goods_name,goods_image,gc_id,virtual_indate,virtual_invalid_refund,spec_name,goods_spec", $real_order_info['store_id']);
                                $order_goods_info = $model_order->getOrderGoodsInfo(['rec_id' => $vr_goods_info['rec_id']], "commis_rate,is_dis,dis_member_id,customer_service_id,customer_service_rate,dis_commis_rate,out_member_id,is_live");
                                $order['order_sn'] = $order_info['order_sn'];
                                $order['store_id'] = $real_order_info['store_id'];
                                $order['trade_no'] = $real_order_info['trade_no'];
                                $order['store_name'] = $real_order_info['store_name'];
                                $order['buyer_id'] = $real_order_info['buyer_id'];
                                $order['buyer_name'] = $real_order_info['buyer_name'];
                                $order['buyer_phone'] = $real_order_info['buyer_phone'];
                                $order['encrypt_mobile'] = $real_order_info['encrypt_mobile'];
                                $order['buyer_msg'] = $order_common_info['order_message'];
                                $order['add_time'] = $real_order_info['add_time'];
                                $order['order_state'] = ORDER_STATE_PAY;
                                $order['order_amount'] = isset($order_info['order_amount']) ? floatval($order_info['order_amount']) / 100 : 0;
                                $order['goods_id'] = $vr_goods_info['goods_id'];
                                $order['goods_name'] = $goods_info['goods_name'];
                                $order['goods_price'] = $vr_goods_info['goods_price'];
                                $order['goods_num'] = $vr_goods_info['goods_num'];
                                $order['goods_image'] = $goods_info['goods_image'];
                                $order['commis_rate'] = $order_goods_info['commis_rate'];
                                $order['gc_id'] = $goods_info['gc_id'];
                                $order['vr_indate'] = $goods_info['virtual_indate'];
                                $order['vr_invalid_refund'] = $goods_info['virtual_invalid_refund'];
                                $order['order_from'] = $real_order_info['order_from'];
                                $order['voucher_price'] = isset($order_info['promotion_total']) ? floatval($order_info['promotion_total']) / 100 : 0;
                                $order['voucher_code'] = 0;
                                $order['order_common'] = '';
                                $order['order_type'] = $real_order_info['order_type'];

                                $dis_member_info = [];
                                if (C('distribute_isuse') == 1) { //分销商品
                                    $model_dis_member_fans = Model('dis_member_fans');
                                    $dis_member_info = $model_dis_member_fans->getDisMemberInfo($order['buyer_id']);
                                    $model_dis_member_fans->delDisMemberTemFans($order['buyer_id']);
                                }
                                $order['is_dis'] = $order_goods_info['is_dis'];
                                $order['dis_member_id'] = $order_goods_info['dis_member_id'];
                                $order['customer_service_id'] = $order_goods_info['customer_service_id'];
                                $order['customer_service_rate'] = $order_goods_info['customer_service_rate'];
                                $order['dis_commis_rate'] = $order_goods_info['dis_commis_rate'];
                                $order['out_member_id'] = $order_goods_info['out_member_id'];
                                $order['dis_type'] = $real_order_info['dis_type'];
                                $order['chain_id'] = $real_order_info['chain_id'] ? $real_order_info['chain_id'] : 0;

                                //记录消费者保障服务
                                //处理商品消费者保障服务信息
                                $goods_list = $model_goods->getGoodsContract(array(0 => $goods_info));
                                $goods_infos = $goods_list[0];
                                $contract_itemid_arr = $goods_infos['contractlist'] ? array_keys($goods_infos['contractlist']) : array();
                                $order['goods_contractid'] = $contract_itemid_arr ? implode(',', $contract_itemid_arr) : '';
                                //规格
                                $_tmp_name = unserialize($goods_info['spec_name']);
                                $_tmp_value = unserialize($goods_info['goods_spec']);
                                if (is_array($_tmp_name) && is_array($_tmp_value)) {
                                    $_tmp_name = array_values($_tmp_name);
                                    $_tmp_value = array_values($_tmp_value);
                                    $new_array = [];
                                    foreach ($_tmp_name as $sk => $sv) {
                                        $new_array['goods_spec'] .= $sv . '：' . $_tmp_value[$sk] . ',';
                                    }
                                    $goods_info['goods_spec'] = rtrim($new_array['goods_spec'], ',');
                                } else {
                                    $goods_info['goods_spec'] = null;
                                }
                                $order['goods_spec'] = $goods_info['goods_spec'];
                                $order['is_live'] = $order_goods_info['is_live'];
                                $order['erp_status'] = 1;
                                $order['erp_order_status'] = 2; //ERP订单状态(1-未支付，2-已支付，3-已退款，4-已取消)
                                $order['erp_order_ids'] = serialize($orderadd);
                                $order['erp_mobile'] = $order['buyer_phone'];
                                $order['erp_time'] = time();

                                // 198会员购买
                                if (Goods::isVipGood($order['goods_id'])) {
                                    action(new VipOrderPaidAction($order, $order));
                                }

                                $order_id = $model_vr_order->addOrder($order);
                                if (!$order_id) {
                                    throw new Exception('虚拟订单生成失败');
                                }
                                $order['order_id'] = $order_id;
                                $insert = $model_vr_order->addOrderCode($order, $data['order_codeinfo']);
                                if (!$insert) {
                                    throw new Exception('兑换码生成失败');
                                }

                                //订单支付成功修改分销员绑定关系
                                $distribute_logic = Logic('distribute');
                                $distribute_logic->editDisMemberFans($order['buyer_id']);
                            }
                        }
                    }
                } else { //实物订单
                    if (empty($real_order_info = $queriedOrders[$order_info['old_order_sn']])) {
                        $queriedOrders[$order_info['old_order_sn']] = $real_order_info = $model_order->getOrderInfo(['order_sn' => $order_info['old_order_sn']], [], "", '', '', true);
                    }

                    if (empty($goods_list_data)) {
                        throw new Exception('订单商品信息不能为空');
                    }
                    if (intval($real_order_info['lock_state']) > 0 || in_array($real_order_info['order_state'], array(0, 40))) {
                        continue;
                    }
                    if (is_array($real_order_info) && !empty($real_order_info)) {
                        $order['order_sn'] = $order_info['order_sn'];
                        $order['pay_sn'] = $real_order_info['pay_sn'];
                        $order['store_id'] = $real_order_info['store_id'];
                        $order['store_name'] = $real_order_info['store_name'];
                        $order['buyer_id'] = $real_order_info['buyer_id'];
                        $order['buyer_name'] = $real_order_info['buyer_name'];
                        $order['buyer_email'] = $real_order_info['buyer_email'];
                        $order['buyer_phone'] = $real_order_info['buyer_phone'];
                        $order['encrypt_mobile'] = $real_order_info['encrypt_mobile'];
                        $order['add_time'] = $real_order_info['add_time'];;
                        $order['payment_code'] = $real_order_info['payment_code'];
                        $order['order_state'] = $real_order_info['order_state'];
                        $order['order_amount'] = $order_info['order_amount'] ? floatval($order_info['order_amount']) / 100 : 0;
                        if ($real_order_info['order_type'] == 19) {
                            $order['shipping_fee'] = $real_order_info['shipping_fee'];
                        } else {
                            $order['shipping_fee'] = $order_info['shipping_fee'] ? floatval($order_info['shipping_fee']) / 100 : 0;
                        }
                        $order['goods_amount'] = $order_info['goods_amount'] ? floatval($order_info['goods_amount']) / 100 : 0;
                        $order['order_type'] = $real_order_info['order_type'];
                        $order['order_from'] = $real_order_info['order_from'];
                        // 巨星药品仓
                        $order['warehouse_type'] = $order_info['warehouse_code'] == 'JXYQC01' ? 1 : 0;
                        $power_id = $order_info['power_id'];
                        $power_state = true;
                        if ($power_id) {
                            $power_info = $power_model->getPowerInfo(['power_id' => $power_id, 'power_status' => 1]);
                            if ($power_info) {
                                //保存数据
                                $power_state = false;
                            }
                        }

                        $order['chain_id'] = $real_order_info['chain_id'];
                        $order['rpt_amount'] = $real_order_info['rpt_amount'];
                        $order['dis_type'] = $real_order_info['dis_type'];
                        $order['is_live'] = $real_order_info['is_live'];
                        $order['first_order'] = $real_order_info['first_order'];
                        $order['order_father'] = $real_order_info['order_id'];
                        $order['payment_from'] = $real_order_info['payment_from'];
                        $order['order_demolition'] = 0;
                        $order['is_dis'] = $real_order_info['is_dis'];
                        $order['trade_no'] = $real_order_info['trade_no'];
                        $order['payment_time'] = $real_order_info['payment_time'];
                        $order['rcb_amount'] = $real_order_info['rcb_amount'];
                        $order['pd_amount'] = $real_order_info['pd_amount'];
                        $order['api_pay_time'] = $real_order_info['api_pay_time'];
                        //周期购
                        $order['is_head'] = $real_order_info['is_head'];
                        $order['cycle_num'] = $real_order_info['cycle_num'];
                        $order['is_use_virtual_stock'] = $real_order_info['is_use_virtual_stock'];
                        // 互联网医疗订单信息
                        $order['hospital_recommend_id'] = $real_order_info['hospital_recommend_id'];
                        //宠商云注册业务员
                        $order['tuoke_salesperson_id'] = $real_order_info['tuoke_salesperson_id'];

                        $order_id = $model_order->addOrder($order);

                        if (!$order_id) {
                            throw new Exception('订单保存失败[未生成订单数据]');
                        }
                        $order['order_id'] = $order_id;
                        $order_list[$order_id] = $order;

                        $order_common_info = $model_order->getOrderCommonInfo(array('order_id' => $real_order_info['order_id']));
                        $order_common['order_id'] = $order_id;
                        $order_common['store_id'] = $order_common_info['store_id'];
                        $order_common['order_message'] = $order_common_info['order_message'];
                        $order_common['voucher_price'] = $order_common_info['voucher_price'];
                        $order_common['voucher_code'] = $order_common_info['voucher_code'];

                        $order_common['promotion_total'] =  $order_info['promotion_total'] / 100;

                        if ($order_common_info['seller_discount_price'] != 0) {
                            // 最后一单
                            if (count($order_data) == ($key + 1)) {
                                $order_common['seller_discount_price'] = $order_common_info['seller_discount_price'] - $historyCount['seller_discount_price'];
                            } else {
                                if ($order_common_info['voucher_price'] > 0) {
                                    $rate = $order['order_amount'] / $real_order_info['order_amount'];
                                    $order_common['seller_discount_price'] = round($order_common_info['seller_discount_price'] * $rate, 2);
                                    $historyCount['seller_discount_price'] += $order_common['seller_discount_price'];
                                } else {
                                    $order_common['seller_discount_price'] = $order_common['promotion_total'];
                                    $historyCount['seller_discount_price'] += $order_common['seller_discount_price'];
                                }
                            }
                        }
                        $order_common['promotion_total'] -= $order_common['seller_discount_price'] ?: 0;
                        $order_common['reciver_info'] = $order_common_info['reciver_info'];
                        $order_common['reciver_name'] = $order_common_info['reciver_name'];
                        $order_common['reciver_city_id'] = $order_common_info['reciver_city_id'];
                        $order_common['reciver_province_id'] = $order_common_info['reciver_province_id'];

                        //发票信息
                        $order_common['invoice_info'] = $order_common_info['invoice_info'];
                        $order_common['promotion_info'] = $order_common_info['promotion_info'];
                        $order_common['reciver_date_msg'] = $order_common_info['reciver_date_msg'];

                        $insert = $model_order->addOrderCommon($order_common);
                        if (!$insert) {
                            throw new Exception('订单保存失败[未生成订单扩展数据]');
                        }

                        //判断宠商云订单是否分销订单及业务员订单,则生成业务员订单数据
                        if ($order['store_id'] == 3 && $order['is_dis'] > 0) {
                            SyncOrdersSalespersonQueue::dispatch([
                                'parend_order_id' => $real_order_info['order_id'],
                                'order_id' => $order_id,
                                'order_sn' => $order['order_sn']
                            ]);
                            //获取redis中的数据
                            $redis_data = RedisManager::get('updateDisOrderAmount_data_' . $order_info['old_order_sn']);
                            if ($redis_data) {
                                $redis_data = json_decode($redis_data, true);
                                $redis_data['type'] = 2;
                                SyncDisDistributorFansOrder::dispatch($redis_data);
                            }
                        }

                        // 当store_id=2且有voucher_code时，异步更新eshop库pet_prize表的核销状态
                        if ($order['store_id'] == 2 && !empty($order_common['voucher_code'])) {
                            UpdatePetPrizeStatusQueue::dispatch($order_common['voucher_code']);
                        }

                        // 定金预售
                        if ($order['order_type'] == 11) {
                            $presale = Model()->table('order_presale')->where([
                                'erp_order_sn' => $real_order_info['order_sn']
                            ])->find();

                            if (empty($presale)) {
                                throw new Exception('订单保存失败[找不到预售信息]');
                            }

                            unset($presale['id']);
                            $presale['erp_order_sn'] = $order['order_sn'];

                            // 拆成多个订单
                            if (count($order_data) > 1) {
                                $presale['pre_price'] = ($presale['pre_price'] / $presale['last_price']) * $order['order_amount'];
                                $presale['last_price'] = $order['order_amount'] - $presale['pre_price'];
                            }

                            $insert = Model()->table('order_presale')->insert($presale);

                            if (empty($insert)) {
                                throw new Exception('订单保存失败[未保存定金数据]');
                            }
                        }

                        //添加订单日志
                        $log_data = array();
                        $log_data['order_id'] = $order_id;
                        $log_data['log_role'] = '买家';
                        $log_data['log_msg'] = '生成订单';
                        $log_data['log_user'] = $real_order_info['buyer_name'];
                        $log_data['log_orderstate'] = ORDER_STATE_NEW;
                        $model_order->addOrderLog($log_data);

                        //生成order_goods订单商品数据
                        $i = 0;
                        $goods_arr = []; //变更销量
                        $purchase = Sku::whereIn('id', array_column($goods_list_data, 'goods_id'))->column('r1_purchase_price', 'id');

                        $inName = [];
                        foreach ($goods_list_data as $goods_info) {
                            $order_goods_info = $model_order->getOrderGoodsInfo(['rec_id' => $goods_info['rec_id']]);
                            if ($order_goods_info['goods_type'] == 17) {
                                $inName[$goods_info['rec_id']] = $order_goods_info['goods_name'];
                            }
                            $pushGoodsInfo = $model_goods->getGoodsInfo(array('goods_id' => $goods_info['goods_id'], 'store_id' => $order['store_id']), "goods_image,goods_price");
                            $goods_commonid = $order_goods_info['goods_commonid'];
                            $order_goods[$i]['order_id'] = $order_id;
                            $order_goods[$i]['goods_id'] = $goods_info['goods_id'];
                            $order_goods[$i]['store_id'] = $order_goods_info['store_id'];
                            $order_goods[$i]['goods_name'] = html_entity_decode($goods_info['goods_name']);
                            $order_goods[$i]['goods_price'] = $goods_info['goods_price'] ? floatval($goods_info['goods_price']) / 100 : 0;

                            // 子订单也记录采购价
                            if ($order_goods_info['purchase_price'] > 0 && $order_goods_info['goods_id'] == $goods_info['goods_id']) {
                                $order_goods[$i]['purchase_price'] = $order_goods_info['purchase_price'];
                            } else if (isset($purchase[$goods_info['goods_id']])) {
                                $order_goods[$i]['purchase_price'] = $purchase[$goods_info['goods_id']] / 100;
                            }

                            if ($order_goods_info['is_group_goods'] || $order_goods_info['goods_type'] == 13) {
                                $order_goods[$i]['promotion_type'] = 6;
                                $order_goods[$i]['goods_original_price'] = $pushGoodsInfo['goods_price'];
                                $order_goods[$i]['goods_type'] = 1;
                            } else {
                                $order_goods[$i]['goods_original_price'] = $order_goods_info['goods_original_price'];
                            }

                            $order_goods[$i]['goods_type'] = $order_goods_info['goods_type'];
                            $order_goods[$i]['goods_num'] = $goods_info['goods_num'];
                            $order_goods[$i]['goods_image'] = $pushGoodsInfo['goods_image'];
                            $order_goods[$i]['goods_spec'] = $order_goods_info['goods_spec'];
                            $order_goods[$i]['buyer_id'] = $order_goods_info['buyer_id'];
                            $order_goods[$i]['goods_commonid'] = $goods_commonid;
                            $order_goods[$i]['add_time'] = $order_goods_info['add_time'];;
                            $order_goods[$i]['sku'] = $order_goods_info['sku'];

                            //是否存在APP推荐ID
                            if (isset($order_goods_info['app_order_id'])) {
                                $order_goods[$i]['app_order_id'] = $order_goods_info['app_order_id'];
                            }

                            $order_goods[$i]['chain_id'] = $order_goods_info['chain_id']; //增加门店id记录
                            $order_goods[$i]['promotions_id'] = $order_goods_info['promotions_id'];
                            $order_goods[$i]['commis_rate'] = $order_goods_info['commis_rate'];
                            $order_goods[$i]['gc_id'] = $order_goods_info['gc_id'];
                            //记录消费者保障服务
                            $order_goods[$i]['goods_contractid'] = $order_goods_info['goods_contractid'];
                            //计算本件商品优惠金额
                            $order_goods[$i]['goods_pay_price'] = $goods_info['goods_pay_price'] ? floatval($goods_info['goods_pay_price']) / 100 : 0; //$goods_total - $promotion_value < 0 ? 0 : $goods_total - $promotion_value;
                            $order_goods[$i]['is_dis'] = $order_goods_info['is_dis'];
                            $order_goods[$i]['dis_member_id'] = $order_goods_info['dis_member_id'];
                            //                            $order_goods[$i]['salesperson_id'] = $order_goods_info['salesperson_id'];
                            $order_goods[$i]['customer_service_id'] = $order_goods_info['customer_service_id'];
                            $order_goods[$i]['customer_service_rate'] = $order_goods_info['customer_service_rate'];
                            $order_goods[$i]['out_member_id'] = $order_goods_info['out_member_id'];
                            $order_goods[$i]['dis_commis_rate'] = $order_goods_info['dis_commis_rate'];
                            $order_goods[$i]['is_live'] = $order_goods_info['is_live'];
                            $order_goods[$i]['oc_id'] = $goods_info['oc_id'];
                            $order_goods[$i]['voucher_info'] = json_encode($goods_info['voucher_info']);
                            //是否使用虚拟库存，默认0否，1是
                            $order_goods[$i]['is_use_virtual_stock'] = $order_goods_info['is_use_virtual_stock'];
                            $goods_arr[$goods_info['goods_id']] = $goods_info['goods_num'];
                            $order_goods[$i]['shop_id'] = $order_goods_info['shop_id'];
                            $i++;
                        }

                        $insert = $model_order->addOrderGoodsOne($order_goods);

                        if (!empty($insert)) {
                            $insert_id = $insert;
                        }

                        if ($inName) {
                            MemberPropertyGuaranteeQuota::freezeDecr([
                                'order_sn' => $real_order_info['pay_sn'], // 支付单号
                                'order_id' => 'r-' . $order_id, // v-23,r-23 子订单id，跳转详情使用
                                'order_amount' => $real_order_info['order_amount'], // 实付金额
                                'goods_total' => $real_order_info['goods_amount'] + $order_common['promotion_total'], // 商品总金额
                                'goods_name' => implode('，', array_values($inName)), // 商品名称
                                'goods_id' => $real_order_info['goods_id'],
                                'buyer_id' => $real_order_info['buyer_id']
                            ]);
                        }

                        if (empty($insert)) {
                            throw new Exception('订单保存失败[未生成商品数据01]');
                        }
                        //修改助力下单成功回填订单号
                        if (!$power_state) {
                            $power_model->editPower(['order_id' => $order_id, 'power_status' => 2], ['power_id' => $power_id]);
                        }

                        //周期购更新订单号 推送服务通知
                        if ($order_info['order_type'] == 9) {
                            Model('cycle_push_info')->editCycleOrder($order_info);
                        }

                        if ($insert_id) {
                            $update_data = array();
                            $update_data['order_demolition'] = 1; //'拆单状态 默认0，1为拆单'
                            $update_data['delete_state'] = 1; //放入回收站
                            $update_data['order_state'] = 0; //父订单更新为已取消状态
                            $condition = array();
                            $condition['order_id'] = $real_order_info['order_id'];
                            $update = $model_order->editOrder($update_data, $condition);
                            if (!$update) {
                                throw new Exception('订单更新失败[保存失败]');
                            }
                        }
                    } else {
                        throw new Exception('电商订单数据为空');
                    }
                }
            }
            $model_goods->commit();

            return callback(true, '订单创建成功');
        } catch (Exception $e) {
            $model_goods->rollback();
            RedisManager::lPush('erp_datacenterOrderRes', json_encode(['time' => date('Y-m-d H:i:s'), 'param' => $order_info, 'result' => $e->getMessage()], JSON_UNESCAPED_UNICODE));
            return callback(false, $e->getMessage());
        }
    }

    /**
     * 推送积分订单给数据中心
     * @param $pointorderinfo
     * @return bool
     */
    //    public function syncPointsOrderAdd($pointorderinfo,$mobile){
    //        $point_orderid = $pointorderinfo['point_orderid'];
    //        $model_pointorder=Model("pointorder");
    //        //$pointorderinfo=$model_pointorder->getPointOrderInfo(array("point_orderid"=>$point_orderid));
    //        //判断是否存在
    //        if($pointorderinfo){
    //            $model = Model();
    //            $field = '*';
    //            $on = 'points_ordergoods.point_goodsid=points_goods.pgoods_id';
    //            $model->table('points_ordergoods,points_goods')->field($field);
    //            $goods_list = $model->join('inner')->on($on)->where(array('points_ordergoods.point_orderid'=>$point_orderid))->select();
    //            $order_split = array();
    //            $goods_amount = Logic('buy')->intval_format_amount($pointorderinfo['point_allpoint']);
    //            $order_split['order']['old_order_sn'] = $pointorderinfo['point_ordersn'];//订单编号
    //            $order_split['order']['buyer_memo'] = "积分兑换";//买家留言
    //            $order_split['order']['channel_id'] = 5;//渠道ID：1阿闻到家,2美团,3饿了么,4京东到家,5阿闻电商,6门店
    //            $order_split['order']['expected_time'] = "";//预计送达时间
    //            $order_split['order']['freight'] = 0;//总运费
    //            $order_split['order']['goods_total'] = $goods_amount;//商品总金额
    //            $order_split['order']['invoice'] = "";//发票信息
    //            $order_split['order']['is_split'] = 0;//是否有拆单，0否1是
    //            $order_split['order']['latitude'] = 0;//收货地址纬度
    //            $order_split['order']['longitude'] = 0;//收货地址经度
    //            $order_split['order']['order_type'] = 8;//订单类型1普通订单(默认),2预定订单,3门店自提(电商订单专用),4拼团订单,5门店配送,6健康计划,7保险订单,8积分订单
    //            $order_split['order']['privilege'] = 0;//总优惠金额
    //            if ($pointorderinfo['point_goodstype'] > 1) { //虚拟商品，优惠券
    //                $order_split['order']['is_virtual'] = 1;//是否是虚拟订单，0否1是
    //                $order_split['order']['receiver_address'] = "";//收件地址
    //                $order_split['order']['receiver_state'] = "";//收件省
    //                $order_split['order']['receiver_city'] = "";//收件市
    //                $order_split['order']['receiver_district'] = "";//收件区
    //                $order_split['order']['receiver_name'] = "";//收件人
    //                $order_split['order']['receiver_phone'] = $mobile;//收件电话
    //                $order_split['order']['shop_id'] = "";//商户或门店id
    //                $order_split['order']['shop_name'] = "";//商户名称
    //                $order_split['order']['total'] = $goods_amount;//总金额（付款金额，加上运费，减优惠金额）
    //                $order_split['order']['user_agent'] = 3;//渠道来源,1-Android,2-iOS,3-小程序,4-公众号,5-Web,6-其它
    //                $order_split['order']['order_pay_type'] = Goods::getVirtualOrderType($goods_list[0]['pggoods_scrminfo']);
    //
    //                $goods_image_url = pointprodThumb($goods_list[0]['pgoods_image'], 'small');
    //                $order_split['order_products'][0]['child_product_list'] = [];// 子商品列表
    //                $order_split['order_products'][0]['combine_type'] = 0;// 子商品列表
    //                $order_split['order_products'][0]['discount_count'] = 0;// 参与限时折扣的商品数量
    //                $order_split['order_products'][0]['discount_price'] = 0;// 折扣价格
    //                $order_split['order_products'][0]['image'] = isset($goods_list[0]['pgoods_image']) ? (string)$goods_image_url : "";// 商品图片
    //                $order_split['order_products'][0]['is_have_reality'] = 0;// 针对组合商品，是否有实物商品（0-没有1-有）
    //                $order_split['order_products'][0]['number'] = intval($goods_list[0]['point_goodsnum']);// 数量
    //                $order_split['order_products'][0]['parent_sku'] = "";// 组合商品父级sku
    //                $order_split['order_products'][0]['price'] = Logic('buy')->intval_format_amount($goods_list[0]['point_goodspoints'] * 100);// 单价
    //                $order_split['order_products'][0]['product_id'] = (string)$goods_list[0]['pggoods_scrminfo'];// 商品id
    //                $order_split['order_products'][0]['product_name'] = (string)$goods_list[0]['point_goodsname'];// 商品名称
    //                $order_split['order_products'][0]['product_type'] = 2;// 商品类型1-实物商品，2-虚拟商品，3-组合商品
    //                $order_split['order_products'][0]['promotion_id'] = 0;// 促销活动Id
    //                $order_split['order_products'][0]['promotion_type'] = 0;// 活动类型1-满减商品2限时折扣3-满减运费
    //                $order_split['order_goods'][0]['source'] = 0;
    //                $order_split['order_products'][0]['sku'] = (string)$goods_list[0]['pggoods_scrminfo'];//isset($goods_info['goods_serial']) ? (string)$goods_info['goods_serial'] : "";
    //                $bar_code = "";
    //                $term_type = 0;
    //                $term_value = 0;
    //                $virtual_invalid_refund = 0;
    //                if ($pointorderinfo['point_goodstype'] = 2) {//2虚拟3优惠券
    //                    $goodsfield = 'goods_serial,goods_price,virtual_invalid_refund,virtual_indate,goods_barcode';
    //                    $vr_goods_info = Model("goods")->getGoodsInfoByID($goods_list[0]['pggoods_scrminfo'],$goodsfield);
    //                    $bar_code = $vr_goods_info['goods_barcode'];
    //                    $term_type = 1;
    //                    $term_value = intval($vr_goods_info['virtual_indate']);
    //                    $virtual_invalid_refund = intval($vr_goods_info['virtual_invalid_refund']);
    //                }
    //                $order_split['order_products'][0]['bar_code'] = (string)$bar_code;
    //                $order_split['order_products'][0]['term_type'] = $term_type;
    //                $order_split['order_products'][0]['term_value'] = $term_value;
    //                $order_split['order_products'][0]['virtual_invalid_refund'] = $virtual_invalid_refund;
    //                $order_split['order_promotions'] = [];
    //                $order_split['pay_info'] = (object)[];
    //                unset($order_split['order_goods']);
    //                $jsonstr = json_encode($order_split,JSON_UNESCAPED_UNICODE);
    //                list($code, $response) = http_post_json(C('datacenter_orderpay_url') . "/order-api/order/docommit", $jsonstr, $_REQUEST['key'], $order_split['order']['old_order_sn'], 1);
    //                $order_arr = json_decode($response, true);
    //                wkcache("dddpush_datacenter_return_result_" . $order_split['order']['old_order_sn'], $order_arr, 7200);
    //                if ($code == '200' && $order_arr['code'] == '200') {
    //                    $updata=array();
    //                    //$updata['erp_order_id']   = $erp_order_id;
    //                    $updata['erp_status']   = 1;
    //                    $updata['erp_mobile']   = $mobile;
    //                    $updata['erp_time']=time();
    //                    $model_pointorder->editPointOrder(array("point_orderid"=>$point_orderid),$updata);
    //                    return true;
    //                } else {
    //                    return false;
    //                }
    //            } else {//实物商品
    //                $orderAddress = Model('pointorder')->getPointOrderAddressInfo(array('point_orderid'=>$point_orderid),"point_areainfo,point_truename,point_address,point_mobphone");
    //                $area_arr = explode(" ",$orderAddress['point_areainfo']);
    //                $order_split['order']['is_virtual'] = 0;//是否是虚拟订单，0否1是
    //                $order_split['order']['receiver_address'] = $orderAddress['point_areainfo'].$orderAddress['point_address'];//收件地址
    //                $order_split['order']['receiver_state'] = $area_arr[0];//收件省
    //                $order_split['order']['receiver_city'] = $area_arr[1] ? $area_arr[1] : "";//收件市
    //                $order_split['order']['receiver_district'] = $area_arr[2] ? $area_arr[2] : "";//收件区
    //                $order_split['order']['receiver_name'] = $orderAddress['point_truename'];//收件人
    //                $order_split['order']['receiver_phone'] = $orderAddress['point_mobphone'];//收件电话
    //                $order_split['order']['shop_id'] = "";//商户或门店id
    //                $order_split['order']['shop_name'] = "";//商户名称
    //                $order_split['order']['total'] = $goods_amount;//总金额（付款金额，加上运费，减优惠金额）
    //                $order_split['order']['user_agent'] = 3;//渠道来源,1-Android,2-iOS,3-小程序,4-公众号,5-Web,6-其它
    //                $order_split['order']['order_pay_type'] = OrderAlias::ORDER_PAY_TYPE_NOT_DIS;
    //
    //                $goods_image_url = pointprodThumb($goods_list[0]['pgoods_image'], 'small');
    //                $order_split['order_products'][0]['child_product_list'] = [];// 子商品列表
    //                $order_split['order_products'][0]['combine_type'] = 0;// 子商品列表
    //                $order_split['order_products'][0]['discount_count'] = 0;// 参与限时折扣的商品数量
    //                $order_split['order_products'][0]['discount_price'] = 0;// 折扣价格
    //                $order_split['order_products'][0]['image'] = isset($goods_list[0]['pgoods_image']) ? (string)$goods_image_url : "";// 商品图片
    //                $order_split['order_products'][0]['is_have_reality'] = 0;// 针对组合商品，是否有实物商品（0-没有1-有）
    //                $order_split['order_products'][0]['number'] = intval($goods_list[0]['point_goodsnum']);// 数量
    //                $order_split['order_products'][0]['parent_sku'] = "";// 组合商品父级sku
    //                $order_split['order_products'][0]['price'] = Logic('buy')->intval_format_amount($goods_list[0]['point_goodspoints'] * 100);// 单价
    //                $order_split['order_products'][0]['product_id'] = (string)$goods_list[0]['pgoods_serial'];// 商品id
    //                $order_split['order_products'][0]['product_name'] = (string)$goods_list[0]['point_goodsname'];// 商品名称
    //                $order_split['order_products'][0]['product_type'] = 1;// 商品类型1-实物商品，2-虚拟商品，3-组合商品
    //                $order_split['order_products'][0]['promotion_id'] = 0;// 促销活动Id
    //                $order_split['order_products'][0]['promotion_type'] = 0;// 活动类型1-满减商品2限时折扣3-满减运费
    //                $order_split['order_goods'][0]['source'] = 0;
    //                $order_split['order_products'][0]['sku'] = (string)$goods_list[0]['pgoods_serial'];//isset($goods_info['goods_serial']) ? (string)$goods_info['goods_serial'] : "";
    //                $goodsfield = 'goods_serial,goods_price,virtual_invalid_refund,virtual_indate,goods_barcode';
    //                $vr_goods_info = Model("goods")->getGoodsInfoByID($goods_list[0]['pgoods_serial'],$goodsfield);
    //                $order_split['order_products'][0]['bar_code'] = isset($vr_goods_info['goods_barcode']) ? (string)$vr_goods_info['goods_barcode'] : "";
    //                $order_split['order_products'][0]['term_type'] = 0;
    //                $order_split['order_products'][0]['term_value'] = 0;
    //                $order_split['order_products'][0]['virtual_invalid_refund'] = 0;
    //                $order_split['order_products'][0]['rec_id'] = 0;
    //                $order_split['order_products'][0]['is_third_product'] = $pointorderinfo['point_gtype'] = 2 ? 1 : 0;
    //                $order_split['order_promotions'] = [];
    //                $order_split['pay_info'] = (object)[];
    //                unset($order_split['order_goods']);
    //                $jsonstr = json_encode($order_split,JSON_UNESCAPED_UNICODE);
    //                list($code, $response) = http_post_json(C('datacenter_orderpay_url') . "/order-api/order/docommit", $jsonstr, $_REQUEST['key'], $order_split['order']['old_order_sn'], 1);
    //                $order_arr = json_decode($response, true);
    //                wkcache("dddpush_datacenter_return_result_" . $order_split['order']['old_order_sn'], $order_arr, 7200);
    //                if ($code == '200' && $order_arr['code'] == '200') {
    //                    $updata=array();
    //                    //$updata['erp_order_id']   = $erp_order_id;
    //                    $updata['erp_status']   = 1;
    //                    $updata['erp_mobile']   = $mobile;
    //                    $updata['erp_time']=time();
    //                    $model_pointorder->editPointOrder(array("point_orderid"=>$point_orderid),$updata);
    //                    return true;
    //                } else {
    //                    return false;
    //                }
    //            }
    //
    //        }
    //    }
}
