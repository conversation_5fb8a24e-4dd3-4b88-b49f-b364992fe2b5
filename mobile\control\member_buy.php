<?php

/**
 * 购买
 *
 *
 *
 *
 * @copyright  Copyright (c) 2007-2018 ShopNC Inc. (http://www.shopnc.net)
 * @license    http://www.shopnc.net
 * @link       http://www.shopnc.net
 * @since      File available since Release v1.1
 */

use Upet\Integrates\Redis\RedisManager as Redis;
use Upet\Models\DcCycleBuyProduct;
use Upet\Models\DcGroupBuyProduct;
use Upet\Models\DcPinOrderGroup;
use Upet\Models\GoodsCommonLive;
use Upet\Models\Order;
use Upet\Modules\Order\Events\OrderCreated;
use Upet\Modules\Order\Queues\SyncHospitalOrderInfoQueue;
use Upet\Models\Chain;

defined('InShopNC') or exit('Access Invalid!');

class member_buyControl extends mobileMemberControl
{

    public function __construct()
    {
        parent::__construct();
    }

    /**
     * 检测拼团订单
     */
    public function checkPintuanOrderOp()
    {
        $goods_id = intval($_POST['goods_id']);
        $log_id = intval($_POST['log_id']);
        $buynum = intval($_POST['buynum']);;
        $model_pintuan = Model('p_pintuan');
        $where = array();
        $where['start_time'] = array('lt', TIMESTAMP);
        $where['end_time'] = array('gt', TIMESTAMP);
        $where['goods_id'] = $goods_id;
        $where['state'] = 1;
        $state = 0;
        $pintuan_info = $model_pintuan->getGoodsInfo($where);
        if (!empty($pintuan_info) && is_array($pintuan_info)) {

            //判断是否新老用户团 类型:0全民拼团,1老带新团
            if ($pintuan_info['pintuan_type'] == 1 && $log_id > 0) {
                $model_member = Model('member');
                $member_info = $model_member->getMemberInfoByID($this->member_info['member_id']);
                if ($member_info['member_time'] < $pintuan_info['start_time']) {
                    output_error('该活动新用户才能参团');
                }
            }
            if ($buynum > $pintuan_info['max_limit'] && $pintuan_info['max_limit'] > 0) {
                output_error('该商品限拼购' . $pintuan_info['max_limit'] . "件");
            } else {
                $state = 1;
            }
        } else {
            $state = 1;
        }
        if ($state && $log_id) {
            $condition = array();
            $condition['buyer_id'] = $this->member_info['member_id'];
            $condition['lock_state'] = array('eq', 1);
            $condition['log_id'] = $log_id;
            $condition['buyer_type'] = array('eq', 0);
            $orderInfo = $model_pintuan->getOrderInfo(array('log_id' => $condition['log_id']), "min_num");
            $buySelfCount = $model_pintuan->getPinTuanCount($condition); //参团总人数
            if ($buySelfCount > 0) {
                output_error('不能购买自己发起的拼团商品');
            } else {
                $pinOrderCount = $model_pintuan->getPinTuanCount(array('buyer_type' => array('eq', $condition['log_id']), 'lock_state' => 1, 'cancel_time' => 0));
                $pintuanOrderInfo = $model_pintuan->getOrderInfo(array('buyer_type' => array('eq', $condition['log_id']), 'buyer_id' => $condition['buyer_id']), "pay_time,lock_state,cancel_time");
                if (is_array($pintuanOrderInfo) && !empty($pintuanOrderInfo)) {
                    if ($pintuanOrderInfo['pay_time'] == 0 && $pintuanOrderInfo['cancel_time'] == 0) {
                        output_error('您已提交订单，请去订单列表去完成未付款订单');
                    } else if ($pintuanOrderInfo['cancel_time'] > 0) {
                        output_data("1");
                    } else {
                        output_error('您已拼团过此商品');
                    }
                } else if ($pinOrderCount == ($orderInfo['min_num'] - 1)) {
                    output_error('拼团人数已满');
                } else {
                    output_data("1");
                }
            }
        } else {
            output_data("1");
        }
    }

    /**
     * 检测拼团限购
     */
    public function checkPintuanLimitOp()
    {
        $goods_id = intval($_POST['goods_id']);
        $buynum = intval($_POST['buynum']);;
        $model_pintuan = Model('p_pintuan');
        $where = array();
        $where['start_time'] = array('lt', TIMESTAMP);
        $where['end_time'] = array('gt', TIMESTAMP);
        $where['goods_id'] = $goods_id;
        $where['state'] = 1;
        $pintuan_info = $model_pintuan->getGoodsInfo($where);
        if (!empty($pintuan_info) && is_array($pintuan_info)) {
            if ($buynum > $pintuan_info['max_limit']) {
                output_error('该商品限拼购' . $pintuan_info['max_limit'] . "件");
            } else {
                output_data("1");
            }
        } else {
            output_data("1");
        }
    }

    /**
     * 购物车、直接购买第一步:选择收获地址和配置方式
     */
    public function buy_step1Op()
    {
        $cart_id = explode(',', $_POST['cart_id']);
        $power_id = intval($_POST['power_id']);
        $is_pintuan = intval($_POST['pintuan']);
        $gid = intval($_POST['gid']);
        $pin_type = intval($_POST['pin_type']); //0=拼团  1：参团
        $activity_type = intval($_POST['activity_type']); //1预售 2新人 3秒杀

        $store_id = $_REQUEST['store_id'] = intval($_REQUEST['store_id']) ?: 1;

        /** @var buyLogic $logic_buy */
        $logic_buy = logic('buy');
        $logic_buy->setMemberInfo($this->member_info);

        $buy_items = $logic_buy->_parseItems($cart_id);
        if (empty($buy_items)) {
            return output_error('所购商品无效');
        }
        //健康会员卡实体卡 vip-3.1
        $model_goods = Model("goods");
        $isPhysicalCardArr = $model_goods->isPhysicalCard(array_keys($buy_items));
        $isPhysicalCard = 0;    //是否是健康会员卡实体卡
        foreach ($isPhysicalCardArr as $k => $v) {
            if ($v > 0) {
                $isPhysicalCard = $v;
                break;
            }
        }
        if ($isPhysicalCard > 0) {
            if (count($isPhysicalCardArr) != 1) {
                return output_error('健康会员卡只能单独购买');
            }
            $_POST['ifcart'] = 0;
        }

        //得到会员等级
        $model_member = Model('member');
        $member_info = $model_member->getMemberInfoByID($this->member_info['member_id']);
        if (!$member_info['is_buy']) output_error('您没有商品购买的权限,如有疑问请联系客服人员');

        //同步ERP库存，更新（根据SKU、地区）
        if (intval($_POST['address_id']) > 0) {
            $address_info = Model('address')->getDefaultAddressInfo(array('address_id' => intval($_POST['address_id']), 'member_id' => $this->member_info['member_id'], 'store_id' => $store_id));
        } else {
            $address_info = Model('address')->getDefaultAddressInfo(array('member_id' => $this->member_info['member_id'], 'store_id' => $store_id));
        }
        $address_info['mob_phone'] = hideStr($address_info['mob_phone']);
        // 得到购买数据
        try {
            $result = $logic_buy->buystep1($cart_id, $_POST['ifcart'], $this->member_info['member_id'], $this->member_info['store_id'], null, $_POST['ifchain'], $_POST['chain_id'], $address_info['city_id'], $power_id);
        } catch (Exception $e) {
            if ($e instanceof \Upet\Exceptions\SubmitStockException) {
                output_data($e->getStockData());
            }
            return callback(false, $e->getMessage());
        }
        if (!$result['state']) {
            // 存在action时状态码返回200
            output_data(array_merge([
                'error' => $result['msg'],
            ], (array)$result['data']), [], !isset($result['data']['action']));
        } else {
            $result = $result['data'];
        }

        $goods_num_state = 0;
        $cart_goods_num = 0;
        foreach ($result['store_cart_list'] as $key => $value) {
            foreach ($value as $k => $v) {
                if (!$v['storage_state'] || !$v['state']) {
                    $goods_num_state++;
                }
                $cart_goods_num++;
                if (!$v['storage_state'] && empty($v['invalid_type'])) {
                    $result['store_cart_list'][$key][$k]['invalid_type'] = 1;
                }
            }
        }
        $chain_info = array();
        if (intval($_POST['ifchain']) == 1 && intval($_POST['chain_id']) > 0) {
            $chain_info = Model('chain')->getChainInfo(array('chain_id' => intval($_POST['chain_id']), 'chain_state' => 1, 'is_self_take' => 1), 'chain_id,store_id,area_id,chain_name,area_info,chain_address,chain_phone');
            $goods_list = $result['store_cart_list'][$chain_info['store_id']];
            if (!empty($goods_list)) {
                $stock_info = Model('chain_stock')->getChainStockInfo(array('goods_id' => $goods_list[0]['goods_id'], 'stock' => array('gt', 0), 'chain_id' => $chain_info['chain_id']), 'chain_id,chain_price');
            }
            if (!empty($stock_info)) {
                $chain_info['shopnc_chain_price'] = $stock_info['chain_price'];
            }
        }

        if (intval($_POST['address_id']) > 0) {
            $result['address_info'] = $address_info;
        }

        //判断活动是否设置运费0=要运费，1免运费,
        $freight_status = 0;
        //是否叠加优惠券0=不可叠加 1=可叠加
        $pintuan_promotion_table = 1;
        if ($is_pintuan == 1) {
            if ($pin_type == 1) {
                $pin_group_info = DcGroupBuyProduct::getJoinGroupProductInfo($gid, $result['store_cart_list'][$store_id][0]['goods_id']);
            } else {
                $pin_group_info = DcGroupBuyProduct::getGroupProductInfo($gid, $result['store_cart_list'][$store_id][0]['goods_id']);
            }
            $freight_status = $pin_group_info['delivery_free'];
            $pintuan_promotion_table = $pin_group_info['addition_promotion_able'];
        }

        //判断周期购是否设置运费0=要运费，1免运费
        if (intval($_POST['cycle_num']) > 1) {
            $gid = $result['store_cart_list'][$store_id][0]['goods_id'];
            $cycle_info = DcCycleBuyProduct::getCycleProductInfo($gid);
            $freight_status = $cycle_info['is_shipping_free'];
        }


        if ($result['address_info']['address_id']) {
            $data_area = $logic_buy->changeAddr($result['freight_list'], $result['address_info']['city_id'], $result['address_info']['area_id'], $this->member_info['member_id']);

            if (!empty($data_area) && $data_area['content'] && $data_area['state'] == 'success') {
                foreach ($data_area['content'] as $store_id => $value) {
                    //如果收货地址属于偏远地区，都需要运费
                    $transport_id = $logic_buy->getRemoteAreasTransportId($address_info['city_id']);
                    if ($transport_id > 0) {
                        $freight_status = 0;
                    }

                    if ($transport_id == 0 && $power_id > 0) { //助力非偏远地区免运费
                        $freight_status = 1;
                    }

                    // 健康实体卡会员卡直接免运费 vip-3.1
                    if ($chain_info['shopnc_chain_price'] || $freight_status == 1 || $isPhysicalCard > 0) $value = 0; //使用门店自提时免运费
                    if (intval($_POST['cycle_num']) > 1 && $freight_status == 0) {
                        $data_area['content'][$store_id] = bcmul(ncPriceFormat($value), $_POST['cycle_num'], 2);
                    } else {
                        $data_area['content'][$store_id] = ncPriceFormat($value);
                    }
                }
            }
        }

        //整理数据
        $store_cart_list = array();
        $store_total_list = $result['store_goods_total_1'];
        $child_product_list = array();

        $goods_commonids = [];
        foreach ($result['store_cart_list'] as $value) {
            foreach ($value as $v) {
                if ($v['is_group_goods'] > 0) {
                    if (is_array($v['group_goods_list']) && !empty($v['group_goods_list'])) {
                        foreach ($v['group_goods_list'] as $vv) {
                            $goods_commonids[] = $vv['goods_commonid'];
                        }
                    }
                }
            }
        }
        $goodsInfos = [];
        if (!empty($goods_commonids)) {
            $goodsInfoAll = $model_goods->getGoodsInfoInGoodsCommonId(array_unique($goods_commonids), "goods_id,goods_commonid,goods_sku_type,goods_barcode,goods_image,goods_spec,goods_sku,warehouse_type");
            foreach ($goodsInfoAll as $value) {
                if (!isset($goodsInfos[$value['goods_commonid']])) {
                    $goodsInfos[$value['goods_commonid']] = $value;
                }
            }
        }
        foreach ($result['store_cart_list'] as $key => $value) {
            $store_cart_list[$key]['goods_list'] = $value;

            //针对秒杀组合商品构建child_product_list给前端调用docommit方法用-------开始
            foreach ($value as $k => $v) {
                if ($v['is_group_goods'] > 0) {
                    $child_goods_list = $v['group_goods_list'];
                    if (is_array($child_goods_list) && !empty($child_goods_list)) {
                        foreach ($child_goods_list as $kk => $vv) {
                            $vv_goods_info = $goodsInfos[$vv['goods_commonid']];
                            $vv_goods_sku_type = intval($vv_goods_info['goods_sku_type']);
                            $child_product_list[$kk]['discount_count'] = 0;
                            $child_product_list[$kk]['discount_price'] = $logic_buy->intval_format_amount($vv['discount_value'] * 100);
                            $child_product_list[$kk]['image'] = isset($vv['goods_image_url']) ? (string)$vv['goods_image_url'] : "";;
                            $child_product_list[$kk]['specs'] = isset($vv_goods_info['goods_spec']) ? (string)$vv_goods_info['goods_spec'] : "";
                            $child_product_list[$kk]['number'] = intval($vv['goods_number']);
                            $child_product_list[$kk]['parent_sku'] = isset($v['goods_commonid']) ? (string)$v['goods_commonid'] : "";
                            $child_product_list[$kk]['price'] = $logic_buy->intval_format_amount($vv['goods_price'] * 100);
                            $child_product_list[$kk]['product_id'] = isset($vv_goods_info['goods_id']) ? (string)$vv_goods_info['goods_id'] : "";
                            $child_product_list[$kk]['product_name'] = (string)$vv['goods_name'];
                            $child_product_list[$kk]['product_type'] = 1;
                            $child_product_list[$kk]['promotion_id'] = 0;
                            $child_product_list[$kk]['promotion_type'] = 0;
                            $child_product_list[$kk]['source'] = $vv_goods_sku_type ? $vv_goods_sku_type : 1;
                            $child_product_list[$kk]['bar_code'] = $vv_goods_info['goods_barcode'];
                            $child_product_list[$kk]['sku'] = isset($vv_goods_info['goods_id']) ? (string)$vv_goods_info['goods_id'] : "";
                            $child_product_list[$kk]['rec_id'] = 0;
                            $child_product_list[$kk]['combine_type'] = 0;
                            $child_product_list[$kk]['child_product_list'] = [];
                            $child_product_list[$kk]['term_type'] = 0;
                            $child_product_list[$kk]['term_value'] = 0;
                            $child_product_list[$kk]['warehouse_type'] = (int)$vv['warehouse_type'];
                        }
                    }
                }
            }
            //针对秒杀组合商品构建child_product_list给前端调用docommit方法用-------结束

            $store_cart_list[$key]['store_goods_total'] = $result['store_goods_total'][$key];
            $store_cart_list[$key]['store_mansong_rule_list'] = $result['store_mansong_rule_list'][$key];
            if (is_array($result['store_voucher_list'][$key]) && count($result['store_voucher_list'][$key]) > 0 && $pintuan_promotion_table == 1 && !$logic_buy->isHospital()) {
                $store_voucher_list = [];
                foreach ($result['store_voucher_list'][$key] as $val) {
                    if ($val['is_usable'] == 1) {
                        $store_voucher_list[] = $val;
                    }
                }
                $store_cart_list[$key]['store_voucher_info'] = [];
                if (!empty($store_voucher_list) && $activity_type != 3) {
                    reset($store_voucher_list);
                    $store_cart_list[$key]['store_voucher_info'] = current($store_voucher_list);
                    $store_cart_list[$key]['store_voucher_info']['voucher_price'] = ncPriceFormat($store_cart_list[$key]['store_voucher_info']['voucher_price']);
                    $store_total_list[$key] -= $store_cart_list[$key]['store_voucher_info']['voucher_price'];
                }
            } else {
                $store_cart_list[$key]['store_voucher_info'] = array();
            }

            $store_cart_list[$key]['store_voucher_list'] = $result['store_voucher_list'][$key];
            // 20220116 新增互联网医院去掉优惠券 健康会员卡实体卡去掉优惠券
            if ($logic_buy->isHospital() || $isPhysicalCard > 0) {
                $store_cart_list[$key]['store_voucher_list'] = $store_cart_list[$key]['store_voucher_info'] = [];
            }

            //判断是否是助力产品
            if ($power_id && !$_POST['ifcart']) {
                $store_cart_list[$key]['store_voucher_list'] = [];
                $store_cart_list[$key]['store_voucher_info'] = [];
            }
            //
            if (!empty($result['cancel_calc_sid_list'][$key])) {
                $store_cart_list[$key]['freight'] = '0';
                $store_cart_list[$key]['freight_message'] = $result['cancel_calc_sid_list'][$key]['desc'];
            } else {

                $store_cart_list[$key]['freight'] = '1';
            }
            $store_cart_list[$key]['store_name'] = $value[0]['store_name'];
        }

        $buy_list = array();
        $buy_list['store_cart_list'] = $store_cart_list;
        $buy_list['freight_hash'] = $result['freight_list'];
        $buy_list['address_info'] = $result['address_info'];
        $buy_list['ifchain'] = $result['ifchain'] ? '1' : '0';
        $buy_list['chain_store_id'] = $result['chain_store_id'] ?: '';

        if ($data_area['content']) {
            $store_total_list = Logic('buy_1')->reCalcGoodsTotal($store_total_list, $data_area['content'], 'freight');
        }

        if ($_POST['activity_type'] == 3) {
            // 秒杀运费加密传递
            $skFreight = base64_encode(json_encode([
                'address_id' => intval($address_info['address_id']),
                'freight' => isset($data_area['content'][1]) ? round($data_area['content'][1] * 100) : 0,
                'can_delivery' => empty($data_area['no_send_tpl_ids']),
                'goods_id' => intval($store_cart_list[1]['goods_list'][0]['goods_id']),
                'timestamp' => time()
            ]));
            $skSign = md5($skFreight . C('openapi_key'));
            // 来一波随机大小写，看起来和谐
            $skSign = implode('', array_map(function ($s) {
                return rand(0, 1) ? strtoupper($s) : $s;
            }, str_split($skSign)));

            $buy_list['sk_freight_encrypt'] = $skSign . $skFreight;
        }

        $buy_list['order_amount'] = ncPriceFormat(array_sum($store_total_list) - $result['rpt_info']['rpacket_price']);

        $buy_list['address_api'] = $data_area ?: '';
        if ($chain_info['shopnc_chain_price']) $buy_list['chain_info'] = $chain_info;

        // 重新计算合计
        foreach ($store_total_list as $store_id => $value) {
            $store_total_list[$store_id] = ncPriceFormat($value);
            $goodInfo = &$store_cart_list[$store_id]['goods_list'][0];
            // 是定金预售
            if ($goodInfo['activity_type'] == 1) {
                $rest = $value - $goodInfo['pre_info']['deposit'];
                $goodInfo['pre_info']['rest'] = ncPriceFormat($rest < 0 ? 0 : $rest);
                $buy_list['store_cart_list'] = $store_cart_list;
            }
        }

        // 会员卡0元购，buyStep1已经校验过资格了，这里只处理价格展示
        if ($_POST['activity_type'] == 19 && !$_POST['ifcart']) {
            foreach ($buy_list['store_cart_list'] as $store_id => &$cart_list) {
                $cart_list['promotions'] = [[
                    'title' => '开卡礼包0元领',
                    'discount' => $store_total_list[$store_id]
                ]];
                $store_total_list[$store_id] = 0;
            }
            $buy_list['order_amount'] = ncPriceFormat(0);
        }

        $buy_list['store_final_total_list'] = $store_total_list;
        $buy_list['weixin_mini_openidshop'] = $this->member_info['weixin_mini_openidshop'];
        $buy_list['weixin_mini_openid'] = $this->member_info['weixin_mini_openid'];

        $count_where = array();
        $count_where['buyer_id'] = array('eq', $this->member_info['member_id']);
        $count_where['order_state'] = array('gt', 10);
        $count_where['refund_state'] = array('lt', 2);
        $order_count = Model('order')->getOrderCount($count_where);

        if ($order_count <= 0) {
            $vr_count_where = array();
            $vr_count_where['buyer_id'] = array('eq', $this->member_info['member_id']);
            $vr_count_where['order_state'] = array('gt', 10);
            $vr_count_where['refund_state'] = array('lt', 2);
            $order_count += Model('vr_order')->getOrderCount($vr_count_where);
        }

        $buy_list['first_order'] = $order_count > 0 ? 0 : 1;

        //判断是否所有商品都没有库存
        $buy_list['goods_number_state'] = true;
        if ($cart_goods_num == $goods_num_state) {
            $buy_list['goods_number_state'] = false;
        }
        //周期购发货时间
        if (intval($_POST['cycle_num']) > 1) {
            $buy_list['cycle_info'] = Model('order')->getCycleProductByDeliveryTime($_POST['cycle_num']);
        }
        $buy_list['child_product_list'] = $child_product_list;

        output_data($buy_list);
    }

    /**
     * 购物车、直接购买第二步:保存订单入库，产生订单号，开始选择支付方式
     *
     */
    public function buy_step2Op()
    {

        $post_data = $_POST;
        unset($post_data['key']);
        log_info('实物订单提交请求参数', $post_data);

        $model_goods = Model("goods");
        /**
         * 互联网医疗订单提交(订单号都是由电商自己生成)
         * 方式一：咨询单号提交，属于互联网医疗订单
         * 方式二：推荐id提交，无咨询单号，推荐的是电商商品，电商自己生成订单号，属于普通订单
         */
        /** @var buyLogic $logic_buy */
        $logic_buy = logic('buy');
        if ($logic_buy->isHospital()) {
            $this->checkHospitalOrder();
        }
        $logic_buy->setMemberInfo($this->member_info);

        $param = array();
        $param['store_id'] = $_REQUEST['store_id'] ?: 1;
        $param['ifcart'] = $_POST['ifcart'];
        $param['cart_id'] = explode(',', $_POST['cart_id']);
        $param['address_id'] = $_POST['address_id'];
        $param['vat_hash'] = $_POST['vat_hash'];
        $param['offpay_hash'] = $_POST['offpay_hash'];
        $param['offpay_hash_batch'] = $_POST['offpay_hash_batch'];
        $param['pay_name'] = $_POST['pay_name'];
        $param['invoice_id'] = $_POST['invoice_id'];
        $param['rpt'] = $_POST['rpt'];
        $param['chain_id'] = $_POST['chain_id'] ?: 0; //门店id
        $dis_id = intval($_POST['dis_id']);
        $dis_type = intval($_POST['dis_type']);
        $param['power_state'] = intval($_POST['power_state']); //是否原价购买
        $param['cycle_num'] = intval($_POST['cycle_num']);
        $param['delivery_interval'] = intval($_POST['delivery_interval']);

        //健康会员卡实体卡 不能与其他商品混合买， 也不可以加入购物车买 vip-3.1
        $buy_items = $logic_buy->_parseItems($param['cart_id']);
        $isPhysicalCardArr = $model_goods->isPhysicalCard(array_keys($buy_items));
        $isPhysicalCard = 0;    //是否是健康会员卡实体卡
        foreach ($isPhysicalCardArr as $k => $v) {
            if ($v > 0) {
                $isPhysicalCard = $v;
            }
        }
        if ($isPhysicalCard > 0) {
            if (count($isPhysicalCardArr) != 1) {
                output_error('健康卡会员卡只能单独购买');
            }
            $param['ifcart'] = $_POST['ifcart'] = 0;
            $param['power_id'] = $_POST['power_id'] = 0;
            $param['cycle_num'] = $_POST['cycle_num'] = 0;
            $_POST['activity_type'] = 0;
            $_POST['pintuan'] == 0;
            $_POST['voucher'] = "";
        }

        //竖屏判断门店财务编码
        if (intval($_POST['source']) == 1) {
            $param['chain_id'] = Chain::getChainId($param['chain_id']);
        }

        $input_address_id = intval($_POST['address_id']);
        if ($input_address_id <= 0) {
            output_error('请选择收货地址');
        } else {
            $param['input_address_info'] = Model('address')->getAddressInfo([
                'address_id' => $input_address_id,
                'member_id' => $this->member_info['member_id']
            ]);

            if (empty($param['input_address_info'])) {
                output_error('请选择收货地址');
            }
            if (empty($param['input_address_info']['house_info'])) {
                output_error('收货地址没有详细门牌号，请重新编辑地址再下单');
            }
        }

        //得到购买商品信息
        try {
            if ($_POST['ifcart']) {
                if ($_POST['activity_type'] == 19) {
                    output_error('礼包商品不允许购物车下单。');
                }
                if ($isPhysicalCard > 0) {
                    // 健康会员卡实体卡不允许购物车下单
                    output_error('该商品不允许购物车下单。');
                }
                $result = $logic_buy->getCartList($param['cart_id'], $this->member_info['member_id'], null, null, $param['input_address_info']['city_id'], 1);
            } else {
                $result = $logic_buy->getGoodsList($param['cart_id'], $this->member_info['member_id'], $this->member_info['store_id'], $param['input_address_info']['city_id'], 1);
            }
        } catch (Exception $e) {
            if ($e instanceof \Upet\Exceptions\SubmitStockException) {
                output_data($e->getStockData());
            }
            return callback(false, $e->getMessage());
        }

        // 存在action时状态码返回200
        if (!$result['state']) {
            output_data(array_merge([
                'error' => $result['msg'],
            ], (array)$result['data']), [], !$result['data']['action']);
        }

        /** @var distributeLogic $distribute_logic*/
        // 互联网医疗不走分销
        if (!$logic_buy->isHospital() && $param['store_id'] != 3) {
            $distribute_logic = Logic('distribute');
            $distribute_logic->goodsMemberDis($dis_id, $this->member_info, 0, $dis_type, intval($_POST['ds_uid']));
        }

        //处理代金券
        $voucher = array();
        if ($isPhysicalCard == 0) {
            $post_voucher = explode(',', $_POST['voucher']);
            if (!empty($post_voucher)) {
                foreach ($post_voucher as $value) {
                    list($voucher_t_id, $store_id, $voucher_price) = explode('|', $value);
                    $voucher[$store_id] = $value;
                }
            }
        }
        $param['voucher'] = $voucher;
        $pt_pay_message = $_POST['pay_message'];
        $_POST['pay_message'] = trim($_POST['pay_message'], ',');
        $_POST['pay_message'] = explode(',', $_POST['pay_message']);
        $param['pay_message'] = array();
        if (is_array($_POST['pay_message']) && $_POST['pay_message']) {
            foreach ($_POST['pay_message'] as $v) {
                if (strpos($v, '|') !== false) {
                    $v = explode('|', $v);
                    $param['pay_message'][$v[0]] = $v[1];
                }
            }
        }
        $param['pd_pay'] = $_POST['pd_pay'];
        $param['rcb_pay'] = $_POST['rcb_pay'];
        $param['password'] = $_POST['password'];
        $param['fcode'] = $_POST['fcode'];

        // 订单来源
        $param['order_from'] = getOrderSource($_POST['source']);

        if ($_POST['source'] == 1 && $this->member_info['weixin_mini_openidshop'] == "" && $_POST['open_id']) {
            Model('member')->editMember(array('member_id' => $this->member_info['member_id']), array('weixin_mini_openidshop' => trim($_POST['open_id'])));
        }
        $param['power_id'] = intval($_POST['power_id']) ?: 0;

        $param['dis_type'] = $_POST['dis_type'] ?: 0;
        $param['is_live'] = $_POST['is_live'] ? intval($_POST['is_live']) : 0;
        if ($_POST['from_scene'] == GoodsCommonLive::SCENE) {
            $param['is_live'] = 2;
        }
        $param['first_order'] = $_POST['first_order'] ? intval($_POST['first_order']) : 0;
        //拼团购 验证
        $pintuan = intval($_POST['pintuan']);
        if ($pintuan == 1) { // 用户提交拼团订单
            //判断团是否取消
            if ($_POST['pin_head_order_sn']) {
                $pin_group_status = (new DcPinOrderGroup())->fieldValue(['pin_order_sn' => $_POST['pin_head_order_sn']], 'status');
                if ($pin_group_status == 0) {
                    output_error('抱歉，团已取消，请参与其他团');
                }
            }
            $param['wx_name'] = $_POST['wx_name'];
            $param['wx_img'] = $_POST['wx_img'];
            $param['pin_head_order_sn'] = $_POST['pin_head_order_sn'];
            $param['scrm_user_id'] = $this->member_info['scrm_user_id'];
            $param['weixin_mini_openid'] = $this->member_info['weixin_mini_openid'];
            $param['pt_cart_id'] = $_POST['cart_id'];
            if (isset($_POST['voucher']) && $_POST['voucher']) {
                $voucher = explode(',', $_POST['voucher']);
                if (count($voucher) > 1) {
                    output_error('优惠券异常，请重新选择');
                }
            }
            $param['pt_voucher'] = $_POST['voucher'];
            $param['pt_pay_message'] = $pt_pay_message;
            $param['pt_dis_id'] = $_POST['dis_id'];
            $param['pintuan'] = $pintuan;
            $param['gid'] = intval($_POST['gid']);
            $param['pin_type'] = intval($_POST['pin_type']);
            $param['source'] = intval($_POST['source']);
            $result = $logic_buy->buyStep3($param, $this->member_info['member_id'], $this->member_info['member_name'], $this->member_info['member_email'], $this->member_info['member_mobile']);
        } else if (isset($_POST['from_scene']) && $_POST['from_scene'] == 'hospital') { // 互联网医疗下单
            $result = $logic_buy->buyStepForHospital($param, $this->member_info);
        } else {
            $result = $logic_buy->buyStep2($param, $this->member_info);
        }

        if (!$result['state']) {
            if (stristr($result['msg'], '账号存在异常') !== false) {
                output_data(['error' => $result['msg']], [], true, 4005);
            }
            output_error($result['msg']);
        }

        // 触发实物订单创建事件，处理卡券、商家券核销
        if (isset($result['data']['order_id'])) {
            $order_info = current($result['data']['order_list']);
            event(new OrderCreated($result['data']['order_id']));
        }

        // 互联网订单提交回调通知
        if (isset($_POST['consult_order_sn']) && $_POST['consult_order_sn']) {
            SyncHospitalOrderInfoQueue::dispatch([
                'aw_order_sn' => (string)$result['data']['order_sn'],
                'consult_order_sn' => (string)$_POST['consult_order_sn'],
                'order_status' => 0,
            ]);
        }

        // 有数统计，统一从订单表读取创建时间，以免存在时间差
        if (1 == $pintuan) {
            // 拼团订单是推送到active_pin服务中心处理的，创建时间需要单独查询
            $str_add_time = (new DcPinOrderGroup())->fieldValue(['pin_order_sn' => $result['order_sn']], 'create_time');
            $add_time = strtotime($str_add_time);
        } else {
            $add_time = TIMESTAMP;
        }
        $int_create_time = intval($add_time) > 0 ? $add_time : TIMESTAMP;

        $is_payed = 0;

        if ($_POST['activity_type'] == 19) {
            $is_payed = 1;
        }

        output_data(array(
            'pay_sn' => $result['data']['pay_sn'],
            'order_sn' => $result['data']['order_sn'],
            'payment_code' => $order_info['payment_code'],
            'order_id' => $result['data']['order_id'],
            'is_payed' => $is_payed,
            array('create_time' => str_pad($int_create_time, 13, "0", STR_PAD_RIGHT)),
        ));
    }

    /**
     * 验证密码
     */
    public function check_passwordOp()
    {
        if (empty($_POST['password'])) {
            output_error('参数错误');
        }

        $model_member = Model('member');

        $member_info = $model_member->getMemberInfoByID($this->member_info['member_id']);
        if ($member_info['member_paypwd'] == md5($_POST['password'])) {
            output_data('1');
        } else {
            output_error('密码错误');
        }
    }

    /**
     * 更换收货地址
     */
    public function change_addressOp()
    {
        $logic_buy = Logic('buy');
        if (empty($_POST['city_id'])) {
            $_POST['city_id'] = $_POST['area_id'];
        }

        $data = $logic_buy->changeAddr($_POST['freight_hash'], $_POST['city_id'], $_POST['area_id'], $this->member_info['member_id']);
        if (!empty($data) && $data['state'] == 'success') {
            output_data($data);
        } else {
            output_error('地址修改失败');
        }
    }

    /**
     * 载入门店
     */
    public function load_chainOp()
    {
        $model_chain = Model('chain');
        $model_chain_stock = Model('chain_stock');
        $goods_id = intval($_POST['goods_id']);
        $stock_list = $model_chain_stock->getChainStockList(array('goods_id' => $goods_id, 'stock' => array('gt', 0)), 'chain_id,chain_price');
        $chain_list = array();
        if (!empty($stock_list)) {
            $chainid_array = array();
            $price_list = array();
            foreach ($stock_list as $val) {
                $chainid_array[] = $val['chain_id'];
                $price_list[$val['chain_id']] = $val['chain_price'];
            }
            $chain_list = $model_chain->getChainList(
                array('chain_id' => array('in', $chainid_array), 'area_id' => intval($_POST['area_id']), 'chain_state' => 1, 'is_self_take' => 1),
                'chain_id,chain_name,area_info,chain_address'
            );
            foreach ($chain_list as $k => $v) {
                $v['shopnc_chain_price'] = $price_list[$v['chain_id']];
                $chain_list[$k] = $v;
            }
        }
        output_data(array('chain_list' => $chain_list));
    }

    /**
     * 实物订单支付(新接口)
     */
    public function payOp()
    {
        $pay_sn = $_POST['pay_sn'];
        if (!preg_match('/^\d{18}$/', $pay_sn)) {
            output_error('该订单不存在');
        }

        //查询支付单信息
        $model_order = Model('order');
        $pay_info = $model_order->getOrderPayInfo(array('pay_sn' => $pay_sn, 'buyer_id' => $this->member_info['member_id']), true);
        if (empty($pay_info)) {
            output_error('该订单不存在');
        }

        //取子订单列表
        $condition = array();
        $condition['pay_sn'] = $pay_sn;
        $condition['order_state'] = array('in', array(ORDER_STATE_NEW, ORDER_STATE_PAY));
        $order_list = $model_order->getOrderList($condition, '', '*', '', '', array(), true);
        if (empty($order_list)) {
            output_error('未找到需要支付的订单');
        }

        //定义输出数组
        $pay = array();
        //支付提示主信息
        //订单总支付金额(不包含货到付款)
        $pay['pay_amount'] = 0;
        //充值卡支付金额(之前支付中止，余额被锁定)
        $pay['payed_rcb_amount'] = 0;
        //预存款支付金额(之前支付中止，余额被锁定)
        $pay['payed_pd_amount'] = 0;
        //还需在线支付金额(之前支付中止，余额被锁定)
        $pay['pay_diff_amount'] = 0;
        //账户可用金额
        $pay['member_available_pd'] = 0;
        $pay['member_available_rcb'] = 0;

        $logic_order = Logic('order');

        //计算相关支付金额
        foreach ($order_list as $key => $order_info) {
            if (!in_array($order_info['payment_code'], array('offline', 'chain'))) {
                if ($order_info['order_state'] == ORDER_STATE_NEW) {
                    $pay['payed_rcb_amount'] += $order_info['rcb_amount'];
                    $pay['payed_pd_amount'] += $order_info['pd_amount'];
                    $pay['pay_diff_amount'] += $order_info['order_amount'] - $order_info['rcb_amount'] - $order_info['pd_amount'];
                }
            }
        }
        if ($order_info['chain_id'] && $order_info['payment_code'] == 'chain') {
            $order_list[0]['order_remind'] = '下单成功，请在' . CHAIN_ORDER_PAYPUT_DAY . '日内前往门店提货，逾期订单将自动取消。';
            $flag_chain = 1;
        }

        //如果线上线下支付金额都为0，转到支付成功页
        if (empty($pay['pay_diff_amount'])) {
            output_error('订单重复支付');
        }

        $payment_list = Model('mb_payment')->getMbPaymentOpenList();
        if (!empty($payment_list)) {
            foreach ($payment_list as $k => $value) {
                if ($value['payment_code'] == 'wxpay') {
                    unset($payment_list[$k]);
                    continue;
                }
                unset($payment_list[$k]['payment_id']);
                unset($payment_list[$k]['payment_config']);
                unset($payment_list[$k]['payment_state']);
                unset($payment_list[$k]['payment_state_text']);
            }
        }
        //显示预存款、支付密码、充值卡
        $pay['member_available_pd'] = $this->member_info['available_predeposit'];
        $pay['member_available_rcb'] = $this->member_info['available_rc_balance'];
        $pay['member_paypwd'] = $this->member_info['member_paypwd'] ? true : false;
        $pay['pay_sn'] = $pay_sn;
        $pay['payed_amount'] = ncPriceFormat($pay['payed_rcb_amount'] + $pay['payed_pd_amount']);
        unset($pay['payed_pd_amount']);
        unset($pay['payed_rcb_amount']);
        $pay['pay_amount'] = ncPriceFormat($pay['pay_diff_amount']);
        unset($pay['pay_diff_amount']);
        $pay['member_available_pd'] = ncPriceFormat($pay['member_available_pd']);
        $pay['member_available_rcb'] = ncPriceFormat($pay['member_available_rcb']);
        $pay['payment_list'] = $payment_list ? array_values($payment_list) : array();
        output_data(array('pay_info' => $pay));
    }

    /**
     * AJAX验证支付密码
     */
    public function check_pd_pwdOp()
    {
        if (empty($_POST['password'])) {
            output_error('支付密码格式不正确');
        }
        $buyer_info = Model('member')->getMemberInfoByID($this->member_info['member_id'], 'member_paypwd');
        if ($buyer_info['member_paypwd'] != '') {
            if ($buyer_info['member_paypwd'] === md5($_POST['password'])) {
                output_data('1');
            }
        }
        output_error('支付密码验证失败');
    }

    /**
     * F码验证
     */
    public function check_fcodeOp()
    {
        $goods_id = intval($_POST['goods_id']);
        if ($goods_id <= 0) {
            output_error('商品ID格式不正确');
        }
        if ($_POST['fcode'] == '') {
            output_error('F码格式不正确');
        }
        $result = logic('buy')->checkFcode($goods_id, $_POST['fcode']);
        if ($result['state']) {
            output_data('1');
        } else {
            output_error('F码验证失败');
        }
    }


    /**
     * 互联网订单信息校验
     * 备注：新增逻辑，会存在无咨询单号的情况，但会传推荐id，这时电商自己生成单号
     * consult_order_sn，recommend_id 存到同一个新增字段，方便互联网医疗那边查询
     */
    public function checkHospitalOrder()
    {
        $order_sn = isset($_POST['consult_order_sn']) && $_POST['consult_order_sn'] ? $_POST['consult_order_sn'] : '';
        $recommend_id = isset($_POST['recommend_id']) && $_POST['recommend_id'] ? intval($_POST['recommend_id']) : 0;
        if (!$order_sn && !$recommend_id) {
            output_error('参数缺失！');
        }

        if ($order_sn) { // 咨询单号提交参数校验
            if (strlen($order_sn) > 20 || strlen($order_sn) < 16) {
                output_error('订单号格式错误');
            }
            if (!Redis::set('orderSn_' . $order_sn, 1, ['nx', 'ex' => 3])) {
                output_error('提交太频繁，请稍后再试');
            }
            // 1、未下过单 2、下过单已取消 ，只有这两种可以过，其他的都不让过
            $order = Order::where('hospital_recommend_id', $order_sn)->where('delete_state', 0)->order('order_id', 'desc')->find();
            if ($order && !($order->order_state == 0 && $order->refund_state != 2)) {
                output_error('订单号已存在，请勿重复提交');
            }
        }
    }

    /**
     * Notes:支付时团已取消，请参与其他团
     * User: rocky
     * DateTime: 2022/10/17 9:08
     */
    public function getPinOrderStatusOp()
    {
        if ($_POST['pin_head_order_sn']) {
            //判断团是否取消
            $pin_group_status = (new DcPinOrderGroup())->fieldValue(['pin_order_sn' => $_POST['pin_head_order_sn']], 'status');
            if ($pin_group_status == 0) {
                output_error('抱歉，团已取消，请参与其他团');
            }
        }
        output_data('1');
    }

    /**
     * @desc 百林康原扫码生成订单接口
     * @return array
     */
    public function buyOp()
    {
        /** @var buyLogic $logic_buy */
        $logic_buy = logic('buy');
        $param = array();
        $param['store_id'] = $_POST['store_id'];
        $param['ifcart'] = 0;
        // 物流码
        $param['logistics_code'] = trim($_POST['logistics_code']);
        // 科学喵的物流码 或 贵族的物流码 或贵族的箱码
        $param['code'] = trim($_POST['code']);
        $param['pay_name'] = 'online';
        $param['chain_id'] = 0;
        $param['order_from'] = getOrderSource($_POST['source']);
        $param['input_address_info'] = Model('address')->getAddressInfo([
            'address_id' => 2,
            'member_id' => 0
        ]);

        $logic_buy->setMemberInfo($this->member_info);
        
        $result = $logic_buy->buyStep($param, $this->member_info);
        if (!$result['state']) {
            if (stristr($result['msg'], '账号存在异常') !== false) {
                output_data(['error' => $result['msg']], [], true, 4005);
            }

            output_error($result['msg']);
        }

        output_data([
            'state'=>200,
            'data'=>array(
            'goods_name' => $result['data']['goods_name'],
            'dis_commis_rate' => $result['data']['dis_commis_rate'],
            'goods_num'=>$result['data']['goods_num']
        )]);
    }
}
