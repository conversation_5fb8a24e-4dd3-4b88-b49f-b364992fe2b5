<?php

/**
 * 对外接口地址
 *
 *
 *
 * @copyright  Copyright (c) 2007-2018 ShopNC Inc. (http://www.shopnc.net)
 * @license    http://www.shopnc.net
 * @link       http://www.shopnc.net
 * @since      File available since Release v1.1
 */

use Upet\Integrates\Redis\RedisManager as Redis;
use Upet\Models\CouponActivity;
use Upet\Models\CouponActivityVoucher;
use Upet\Models\Goods;
use Upet\Models\Member as MemberAlias;
use Upet\Models\Order;
use Upet\Models\OrderMain;
use Upet\Models\SyncTask;
use Upet\Models\Tag;
use Upet\Models\VoucherTemplate;
use Upet\Models\VrOrder;
use Upet\Modules\Member\Actions\GetNewcomerTagAction;
use Upet\Modules\Order\Events\OrderCreated;
use Upet\Modules\Order\Queues\SyncHospitalOrderInfoQueue;
use Upet\Modules\Order\Queues\SyncInvoiceInfoQueue;
use Upet\Models\Order as OrderAlias;

defined('InShopNC') or exit('Access Invalid!');
class openapiControl extends mobileHomeControl
{
    public function __construct()
    {
        parent::__construct();
        header("Content-Type: text/html;charset=utf-8");
    }

    public function getExpressInfoOp()
    {
        $e_code = trim($_GET['e_code']);
        $shipping_code = trim($_GET['shipping_code']);
        $sign = trim($_GET['sign']);
        $sure_sign = $this->getSignature(['e_code' => $e_code, 'shipping_code' => $shipping_code]);
        if ($sure_sign == $sign) {
            $express = Model('express')->get_express($e_code, $shipping_code);
            if ($express) {
                output_data($express);
            } else {
                output_error("暂无物流信息");
            }
        } else {
            output_error("无效请求01");
        }
    }
    /**

     * 子龙领取优惠券
     */
    public function exchangeVoucherOp()
    {
        $data = $_POST['data'];
        if (empty($data)) {
            output_error("领券人信息不能为空");
        }
        $sign = trim($_POST['sign']);

        $sure_sign = $this->getSignature(['data' => html_entity_decode($data)]);
        if ($sure_sign == $sign) {
            $model_voucher = Model('voucher');
            $model_member = Model('member');
            $ids_json = htmlspecialchars_decode($data);
            $tid_arr = json_decode($ids_json, true);
            $data_list = [];
            if (is_array($tid_arr) && !empty($tid_arr)) {
                foreach ($tid_arr as $info) {
                    $tid = $info['tid'];
                    $count = $info['num'];
                    if ($info['scrm_user_id']) {
                        $terms = ['scrm_user_id' => $info['scrm_user_id']];
                    } elseif ($info['phone']) {
                        $terms = ['member_mobile' => $info['phone']];
                    }
                    if (empty($terms)) {
                        output_error('参数错误');
                    }
                    $member_info = $model_member->getMemberInfo($terms, "member_id,member_name", false);

                    if (!$member_info['member_id']) {
                        continue;
                    } else {
                        //读取卖家信息
                        $seller_info = Model('seller')->getSellerInfo(array('member_id' => $member_info['member_id']));
                        $store_id = $seller_info['store_id'];
                        $voucher_data = $model_voucher->getCanChangeTemplateInfo($tid, $member_info['member_id'], $store_id);
                        if (
                            !empty($voucher_data['info']['voucher_t_eachlimit']) &&
                            ($count + $voucher_data['voucherone_count']) > $voucher_data['info']['voucher_t_eachlimit']
                        ) {
                            // log_info("step1 超过可兑换数量",$data);
                            output_error("优惠券 $tid 超过可兑换数量");
                        }
                        $voucher_data['info']['buyer_id'] = $member_info['member_id'];
                        $voucher_data['info']['buyer_name'] = $member_info['member_name'];
                        if ($voucher_data['state']) {
                            if ($count > 1) {
                                for ($i = 1; $i <= $count; $i++) {
                                    $data_list[] = $voucher_data['info'];
                                }
                            } else {
                                $data_list[] = $voucher_data['info'];
                            }
                        } else {
                            output_error($voucher_data['msg']);
                        }
                    }
                }
            }
            $code_list = [];
            $i = 0;
            if (is_array($data_list) && !empty($data_list)) {
                try {
                    $model_voucher->beginTransaction();
                    foreach ($data_list as $key => $value) {
                        //添加代金券信息
                        $result = $model_voucher->exchangeVoucherZilong($value, $value['buyer_id'], $value['buyer_name']);
                        if ($result['state'] == false) {
                            throw new Exception($result['msg']);
                        }
                        $code_list[$i]['tid'] = $value['voucher_t_id'];
                        $code_list[$i]['voucherId'] = $result['msg'];
                        $i++;
                    }
                    $model_voucher->commit();
                    if (is_array($code_list) && !empty($code_list)) {
                        output_data($code_list);
                    } else {
                        $model_voucher->rollback();
                        output_error("兑换失败");
                    }
                } catch (Exception $e) {
                    $model_voucher->rollback();
                    output_error($e->getMessage());
                }
            }

            output_error('未发放优惠券，可能是会员无效或优惠券发放限制');
        } else {
            output_error("签名错误");
        }
    }

    /**
     * 冻结,解冻优惠券
     */
    public function voucherHandleOp()
    {
        $voucher_id = intval($_POST['id']);
        $phone = intval($_POST['phone']);
        $type = intval($_POST['type']); //1冻结2解冻
        $sign = trim($_POST['sign']);
        $sure_sign = $this->getSignature(['voucher_id' => $voucher_id, 'phone' => $phone, 'type' => $type]);
        if ($sure_sign == $sign) {
            $model_voucher = Model('voucher');
            $model_member = Model('member');
            $member_info = $model_member->getMemberInfo(['member_mobile' => $phone], "member_id,member_name", false);
            if (!$member_info['member_id']) {
                output_error("会员不存在");
            }
            $voucher_info = $model_voucher->getVoucherInfo(array('voucher_id' => $voucher_id, 'voucher_owner_id' => $member_info['member_id']), "voucher_id,voucher_title,voucher_desc,voucher_price,voucher_state,voucher_freeze");
            if (is_array($voucher_info) && !empty($voucher_info)) {
                if ($type == 1) {
                    if ($voucher_info['voucher_state'] != "1" || $voucher_info['voucher_freeze'] != "0") {
                        output_error('优惠券状态异常');
                    }
                    $result = $model_voucher->editVoucher(['voucher_freeze' => 1], ['voucher_id' => $voucher_id, 'voucher_owner_id' => $member_info['member_id']]);
                    if ($result) {
                        output_data('1');
                    } else {
                        output_error('冻结失败');
                    }
                } else { //解冻
                    if ($voucher_info['voucher_state'] != "1" || $voucher_info['voucher_freeze'] != "1") {
                        output_error('优惠券状态异常');
                    }
                    $result = $model_voucher->editVoucher(['voucher_freeze' => 0], ['voucher_id' => $voucher_id, 'voucher_owner_id' => $member_info['member_id']]);
                    if ($result) {
                        output_data('1');
                    } else {
                        output_error('解冻失败');
                    }
                }
            } else {
                output_error('优惠券信息不存在');
            }
        } else {
            output_error("无效请求01");
        }
    }

    /**
     * 作废优惠券
     */
    public function invalidVoucherOp()
    {
        $voucher_id = trim($_POST['id']);
        $phone = intval($_POST['phone']);
        $sign = trim($_POST['sign']);
        if (!$voucher_id || !$phone || !$sign) {
            output_error("缺少必要参数!");
        }
        $sure_sign = $this->getSignature(['id' => $voucher_id, 'phone' => $phone]); //echo $sure_sign;
        if ($sure_sign == $sign) {
            $model_voucher = Model('voucher');
            $model_member = Model('member');
            $member_info = $model_member->getMemberInfo(['member_mobile' => $phone], "member_id,member_name", false);
            if (!$member_info['member_id']) {
                output_error("会员不存在");
            }
            $voucher_arr = explode(",", $voucher_id);
            //$tip_msg = "";
            $i = 0;
            $new_vid = array();
            $arr_vid = array();
            if (is_array($voucher_arr) && !empty($voucher_arr)) {
                foreach ($voucher_arr as $vid) {
                    $voucher_info = $model_voucher->getVoucherInfo(array('voucher_id' => $vid, 'voucher_owner_id' => $member_info['member_id']), "voucher_id,voucher_state,voucher_freeze");
                    if (in_array($voucher_info['voucher_state'], array(2, 3, 4))) {
                        //$tip_msg .= '优惠券ID:'.$vid.str_replace(array(2,3,4), array('已使用','已过期',"已收回"),$voucher_info['voucher_state']).",";
                        $arr_vid[$i]['id'] = $vid;
                        $arr_vid[$i]['state'] = false;
                        $arr_vid[$i]['message'] = '优惠券ID:' . $vid . str_replace(array(2, 3, 4), array('已使用', '已过期', "已收回"), $voucher_info['voucher_state']);
                        $i++;
                    } elseif ($voucher_info['voucher_state'] == 1) {
                        $new_vid[$i] = $vid;
                        $i++;
                    } else {
                        $arr_vid[$i]['id'] = $vid;
                        $arr_vid[$i]['state'] = false;
                        $arr_vid[$i]['message'] = "优惠券ID:" . $vid . "不存在";
                        $i++;
                    }
                }
            }
            if (!empty($new_vid)) {
                $result = $model_voucher->editVoucher(['voucher_state' => 4], ['voucher_id' => array('in', $new_vid), 'voucher_owner_id' => $member_info['member_id']]);
                if ($result) {
                    $data = array();
                    /*$data['message'] = "作废优惠券成功";
                    if ($tip_msg) {
                        $data['message'] = "作废优惠券成功,".rtrim($tip_msg,",");
                    }*/
                    foreach ($new_vid as $key => $vouche_id) {
                        $data[$key]['id'] = $vouche_id;
                        $data[$key]['state'] = true;
                        $data[$key]['message'] = "作废优惠券成功";
                    }
                    if (!empty($arr_vid)) {
                        $data = array_merge($data, $arr_vid);
                    }
                    output_data($data);
                } else {
                    output_error('作废优惠券失败');
                }
            } elseif (!empty($voucher_arr) && !empty($arr_vid)) {
                //$data = array();
                //$message = rtrim($tip_msg,",");
                output_data($arr_vid);
            } else {
                output_error('没有可作废优惠券');
            }
        } else {
            output_error("无效请求");
        }
    }

    //批量兑换优惠券 预约用
    public function sendVoucherOp()
    {
        $voucher_id = trim($_POST['id']);
        $phone = intval($_POST['phone']);
        $type = $_POST['type'] ? intval($_POST['type']) : 2; //2预约挂号
        $sign = trim($_POST['sign']);
        if (empty($voucher_id) || empty($phone) || empty($sign)) {
            output_error('必填信息不能为空');
        }
        if ($type < 2) {
            output_error('参数值错误');
        }
        $sure_sign = $this->getSignature(['id' => $voucher_id, 'phone' => $phone, 'type' => $type]);
        $_POST['sure_sign'] = $sure_sign;
        wkcache("aw_voucher_param_" . $phone, $_POST, 7200); //todo
        if ($sure_sign == $sign) {
            $model_voucher = Model('voucher');
            $model_member = Model('member');
            $member_info = $model_member->getMemberInfo(['member_mobile' => $phone], "member_id,member_name", false);
            if (!$member_info['member_id']) {
                output_error("会员不存在");
            }
            $voucher_ids = explode(',', $voucher_id);
            //读取卖家信息
            $seller_info = Model('seller')->getSellerInfo(array('member_id' => $member_info['member_id']));

            $store_id = $seller_info['store_id'];
            //验证是否可领取代金券
            $data_list = [];
            $msg = '优惠卷已领取';
            foreach ($voucher_ids as $val) {
                $data = $model_voucher->getCanChangeTemplateInfo($val, $member_info['member_id'], $store_id);
                if ($data['state']) {
                    $data_list[] = $data['info'];
                } else {
                    $msg = $data['msg'];
                }
            }
            if (empty($data_list)) {
                output_error($msg);
            }
            try {
                $model_voucher->beginTransaction();
                //添加代金券信息
                $data = $model_voucher->exchangeVoucherList($data_list, $member_info['member_id'], $member_info['member_name'], $type);
                if ($data['state'] == false) {
                    throw new Exception($data['msg']);
                }
                $model_voucher->commit();
                //1,无限制 2，限单品(SKU) 3,限全品(SPU) 4,限品类  5 限品牌  6 活动页面商品  7，新用户专享   8，老用户专享
                if ($type == 6) { // 答题活动
                    output_data([
                        'voucher_money' => ncPriceFormat($data['money']),
                        'voucher_title' => $data_list[0]['voucher_t_title'],
                        'voucher_end_date' => $data_list[0]['voucher_t_end_date'],
                        'voucher_t_desc' => $data_list[0]['voucher_t_desc'],
                        'voucher_type_rule' => str_replace(array(1, 2, 3, 4, 5, 6, 7, 8), array('无限制', '限单品(SKU)', "限全品(SPU)", "限品类", "限品牌", "活动页面商品", "新用户专享", '老用户专享'), $data_list[0]['voucher_t_type'])
                    ]);
                } else {
                    output_data(ncPriceFormat($data['money']));
                }
            } catch (Exception $e) {
                $model_voucher->rollback();
                output_error($e->getMessage());
            }
        } else {
            output_error("签名错误");
        }
    }

    /**
     * 优惠券列表
     */
    public function voucherListOp()
    {
        $voucher_state = intval($_GET['voucher_state']);
        $curpage = $_GET['curpage'] ? intval($_GET['curpage']) : 1;
        $num = $_GET['page'] ? intval($_GET['page']) : 20;
        $sign = trim($_GET['sign']);
        $sure_sign = $this->getSignature(['voucher_state' => $voucher_state]);
        if ($sure_sign == $sign) {
            $model_voucher = Model('voucher');
            $where = array();
            if ($voucher_state > 0) {
                $where['voucher_state'] = $voucher_state;
            }
            $page_count = $model_voucher->gettotalpage();
            if ($page_count > 20) {
                $limit1 = ($curpage - 1) * $num;
                $limit2 = $num;
                $limit = "{$limit1},{$limit2}";
            } else {
                $limit = 20;
            }
            $field = "voucher_id,voucher_t_id,voucher_title,voucher_limit,voucher_price,voucher_t_type,voucher_start_date,voucher_end_date,voucher_state,wx_coupon_code";
            $voucher_list = $model_voucher->getVoucherList($where, $field, $limit, 0, 'voucher_state asc,voucher_id desc');
            if (is_array($voucher_list) && !empty($voucher_list)) {
                foreach ($voucher_list as $key => $val) {
                    //代金券状态文字
                    $voucher_list[$key]['voucher_state_text'] = str_replace(array(1, 2, 3, 4), array('未使用', '已使用', '已失效', '已失效'), $val['voucher_state']);
                    //代金券有效期
                    $voucher_list[$key]['voucher_start_date_text'] = @date('Y-m-d H:i:s', $val['voucher_start_date']);
                    $voucher_list[$key]['voucher_end_date_text'] = @date('Y-m-d H:i:s', $val['voucher_end_date']);
                    if ($val['voucher_state'] == $model_voucher::VOUCHER_STATE_UNUSED) {
                        $voucher_list[$key]['wx_card_status'] = $val['wx_coupon_code'] ? 2 : 0;
                    }
                    unset($voucher_list[$key]['wx_coupon_code']);
                }
            }
            output_data(array('voucher_list' => $voucher_list), mobile_page($page_count));
        } else {
            output_error("无效请求");
        }
    }

    /**
     * 获取运营中心优惠券设置
     */
    public function querySettingVoucherOp()
    {
        $type = $_GET['type'] ? intval($_GET['type']) : 1; //业务类型：1-预约挂号
        $scene = $_GET['scene'] ? intval($_GET['scene']) : 1; //发券场景：1-预约挂号成功,2-商城支付成功,3-会员卡续费成功
        $voucher_type = $_GET['voucher_type'] ? intval($_GET['voucher_type']) : 1; //优惠券类型：1-商城券,2-门店券,3-本地生活券,4-阿闻平台券
        $sign = trim($_GET['sign']);
        if (empty($type) || empty($scene) || empty($voucher_type)) {
            output_error('必填信息不能为空');
        }
        $sure_sign = $this->getSignature(['type' => $type, 'scene' => $scene, 'voucher_type' => $voucher_type]);
        wkcache("q_coupon_sure_sign", $sure_sign, 7200);
        wkcache("q_coupon_sure_sign_get", $_GET, 7200);
        if ($sure_sign == $sign) {
            $url = BOSS_CENTER_ADDR . "/boss/market/activity/coupon/list?businessType=" . $type . "&state=2&couponScene=" . $scene . "&couponType=" . $voucher_type;
            wkcache("q_coupon_url", $url, 7200);
            $result = doCurlGetRequest($url);
            wkcache("q_coupon_res", $result, 7200);
            $result = json_decode($result, true);
            wkcache("q_coupon_res1", $result, 7200);
            $data = array();
            if ($result['http_code'] == 200) {
                $msg = json_decode($result['msg'], true);
                if (is_array($msg) && $msg['code'] == 200 && !empty($msg['details'])) {
                    $data = $msg['details'];
                }
            }
            output_data($data);
        } else {
            output_error("签名错误");
        }
    }

    /**
     * 订单列表(未使用的) 使用团餐免预约费用用到
     */
    public function payOrderListOp()
    {
        $phone = intval($_GET['phone']);
        $sign = trim($_GET['sign']);
        if (empty($phone) || empty($sign)) {
            output_error('必填信息不能为空');
        }
        $sure_sign = $this->getSignature(['phone' => $phone]);
        $_GET['sure_sign'] = $sure_sign;
        wkcache("aw_tuancan_payorder_param_" . $phone, $_GET, 7200); //todo
        if ($sure_sign == $sign) {
            $model_vr_order = Model('vr_order');
            $model_member = Model('member');
            $member_info = $model_member->getMemberInfo(['member_mobile' => $phone], "member_id,member_name");
            if (!$member_info['member_id']) {
                output_error("会员不存在");
            }
            $condition = array();
            $condition['buyer_id'] = $member_info['member_id'];
            $condition['order_state'] = ORDER_STATE_PAY;
            $order_list = $model_vr_order->getOrderList($condition, 100, 'order_id,goods_id,goods_name,goods_price', 'order_id asc');
            $page_count = $model_vr_order->gettotalpage();
            output_data(array('order_list' => $order_list), mobile_page($page_count));
        } else {
            output_error("签名错误");
        }
    }

    /**
     * 验证虚拟订单信息 使用团餐免预约费用用到
     */
    public function verifyVrOrderInfoOp()
    {
        $order_id = trim($_GET['orderId']);
        $phone = intval($_GET['phone']);
        $sign = trim($_GET['sign']);
        if (empty($order_id) || empty($phone) || empty($sign)) {
            output_error('必填信息不能为空');
        }
        $sure_sign = $this->getSignature(['orderId' => $order_id, 'phone' => $phone]);
        $_GET['sure_sign'] = $sure_sign;
        wkcache("aw_tuancan_verify_payorder_param_" . $phone, $_GET, 7200); //todo
        if ($sure_sign == $sign) {
            $model_vr_order = Model('vr_order');
            $model_member = Model('member');
            $member_info = $model_member->getMemberInfo(['member_mobile' => $phone], "member_id,member_name");
            if (!$member_info['member_id']) {
                output_error("会员不存在");
            }
            $condition = array();
            $condition['order_id'] = $order_id;
            $condition['buyer_id'] = $member_info['member_id'];
            $order_info = $model_vr_order->getOrderInfo($condition, 'order_id,goods_id,goods_name,goods_price');
            if (is_array($order_info) && !empty($order_info)) {
                output_data(array('order_info' => $order_info));
            } else {
                output_error("订单信息不存在");
            }
        } else {
            output_error("签名错误");
        }
    }

    //领取优惠券 吐槽活动用
    public function activitySendVoucherOp()
    {
        $voucher_id = intval($_POST['id']);
        $phone = intval($_POST['phone']);
        $type = $_POST['type'] ? intval($_POST['type']) : 3; //2预约挂号 3 吐槽活动
        $sign = trim($_POST['sign']);
        if (empty($voucher_id) || empty($phone) || empty($sign)) {
            output_error('必填信息不能为空');
        }
        if ($type < 3) {
            output_error('领取类型错误');
        }
        $sure_sign = $this->getSignature(['id' => $voucher_id, 'phone' => $phone, 'type' => $type]);
        $_POST['sure_sign'] = $sure_sign;
        wkcache("aw_voucher_activityparam_" . $phone, $_POST, 7200); //todo
        if ($sure_sign == $sign) {
            $model_voucher = Model('voucher');
            $model_member = Model('member');
            $member_info = $model_member->getMemberInfo(['member_mobile' => $phone], "member_id,member_name", false);
            if (!$member_info['member_id']) {
                output_error("会员不存在");
            }
            //读取卖家信息
            $seller_info = Model('seller')->getSellerInfo(array('member_id' => $member_info['member_id']));
            $store_id = $seller_info['store_id'];
            //验证是否可领取代金券
            $data = $model_voucher->getCanChangeTemplateInfo($voucher_id, $member_info['member_id'], $store_id);
            if ($data['state'] == false) {
                output_error($data['msg']);
            }
            try {
                $model_voucher->beginTransaction();
                //添加代金券信息
                $result = $model_voucher->exchangeVoucherZilong($data['info'], $member_info['member_id'], $member_info['member_name'], $type);
                if ($result['state'] == false) {
                    output_error($result['msg']);
                }
                $model_voucher->commit();
                $new_data = array();
                $new_data['voucher_id'] = $result['msg'];
                $new_data['voucher_title'] = $data['info']['voucher_t_title'];
                $new_data['voucher_desc'] = $data['info']['voucher_t_desc'];
                $new_data['voucher_money'] = $data['info']['voucher_t_price'];
                $new_data['voucher_limit_money'] = $data['info']['voucher_t_limit'];
                $new_data['voucher_start_date'] = $data['info']['voucher_t_start_date'];
                $new_data['voucher_end_date'] = $data['info']['voucher_t_end_date'];
                if ($data['info']['voucher_start_day'] > 0) {
                    if ($data['info']['voucher_start_day'] == 1) {
                        $new_data['voucher_start_date'] = time();
                        $new_data['voucher_end_date'] = strtotime(date('Y-m-d 23:59:59', strtotime("+" . $data['info']['voucher_days'] - 1 . "day")));
                    } else {
                        $new_data['voucher_start_date'] = strtotime(date('Y-m-d', strtotime("+" . ($data['info']['voucher_start_day'] - 1) . "day")));
                        $new_data['voucher_end_date'] = strtotime(date('Y-m-d 23:59:59', strtotime("+" . (($data['info']['voucher_start_day'] - 2) + $data['info']['voucher_days']) . "day")));
                    }
                }
                output_data($new_data);
            } catch (Exception $e) {
                $model_voucher->rollback();
                output_error($e->getMessage());
            }
        } else {
            output_error("签名错误");
        }
    }

    //接收数据中心拆单信息
    public function syncDatacenterOrderOp()
    {
        $data = $_POST['data'];
        if (empty($data)) {
            output_error("订单信息不能为空");
        }
        $sign = trim($_POST['sign']);
        $lock = Redis::lock("syncDatacenterOrder:$sign", 30)->setAutoRelease();
        if (!$lock->get()) {
            output_error('请求处理中');
        }

        $dataJson = stripslashes(html_entity_decode($data));
        $sure_sign = $this->getSignature(['data' => $dataJson]);
        if ($sure_sign != $sign) {
            Model('member')->addApiRequestLog(
                C('mobile_site_url') . '/index.php?act=openapi&op=syncDatacenterOrder',
                null,
                json_encode($_POST, JSON_UNESCAPED_UNICODE),
                "接收通知订单签名错误",
                0,
                3
            );

            output_error("签名错误");
        }

        $order_list = json_decode($dataJson, true);
        $log = [
            C('mobile_site_url') . '/index.php?act=openapi&op=syncDatacenterOrder',
            null,
            $dataJson,
            "接收通知订单成功",
            $order_list[0]['order_info']['old_order_sn'],
            3
        ];
        if (is_array($order_list) && !empty($order_list)) {
            /**@var datacenterLogic $datacenterLogic*/
            $datacenterLogic = Logic('datacenter');
            $result = $datacenterLogic->syncDatacenterOrderInfo($order_list);
            if ($result['state']) {
                // 互联网医疗订单，新增异步回调
                if ($order_list[0]['order_info']['is_virtual'] == 0) {
                    $order_info = Order::where('order_sn', $order_list[0]['order_info']['old_order_sn'])->find();
                    if ($order_info && $order_info->order_type == 13) {
                        SyncHospitalOrderInfoQueue::dispatch([
                            'aw_order_sn' => (string)$order_list[0]['order_info']['order_sn'],
                            'consult_order_sn' => (string)$order_info->hospital_recommend_id,
                            'order_status' => 1,
                        ]);
                    }
                }
                Model('member')->addApiRequestLog(...$log);
                output_data("1");
            } else {
                $log[3] = '订单数据处理失败：' . $result['msg'];
                Model('member')->addApiRequestLog(...$log);
                output_error("订单数据处理失败");
            }
        } else {
            $log[3] = '订单数据解析失败';
            Model('member')->addApiRequestLog(...$log);
            output_error("订单数据解析失败");
        }
    }

    /**
     * 验证订单金额 给数据中心用
     */
    public function verifyOrderInfoOp()
    {
        $order_sn = trim($_POST['order_sn']);
        $amount = floatval($_POST['amount']);
        $type = $_POST['type'] ? intval($_POST['type']) : 2; //1实物 2 虚拟
        $sign = trim($_POST['sign']);
        if (empty($order_sn) || empty($amount) || empty($sign)) {
            output_error('必填信息不能为空');
        }
        if (!in_array($type, array('1', '2'))) {
            output_error('无效请求');
        }
        $sure_sign = $this->getSignature(['order_sn' => $order_sn, 'amount' => $amount, 'type' => $type]);
        if ($sure_sign == $sign) {
            if ($type == 1) {
                $model_order = Model('order');
                $condition = array();
                $condition['order_sn'] = $order_sn;
                $order_info = $model_order->getOrderInfo($condition, array(), 'order_id,order_amount');
                if ($order_info['order_amount'] == $amount) {
                    output_data("1");
                } else {
                    output_error("验证异常");
                }
            } else {
                $model_vr_order = Model('vr_order');
                $condition = array();
                $condition['order_sn'] = $order_sn;
                $order_info = $model_vr_order->getOrderInfo($condition, 'order_id,order_amount');
                if ($order_info['order_amount'] == $amount) {
                    output_data("1");
                } else {
                    output_error("验证异常");
                }
            }
        } else {
            output_error("签名错误");
        }
    }

    /**
     * 支付成功后，支付方式更新通知
     * 备注：针对之前拼团默认支付方式为微信，只有拼团成功才会回调电商，后面新增支付宝和百度支付，导致一直显示微信
     */
    public function updatePayCodeForSuccessOp()
    {
        $data = json_decode(file_get_contents('php://input'), true);
        if (!(is_array($data) && count($data) > 0)) {
            output_error("参数解析失败");
        }

        $sign = $data['sign'];
        unset($data['sign']);
        $sure_sign = $this->getSignature($data);
        if ($sure_sign != $sign) {
            output_error("签名错误");
        }

        // 更新订单支付方式，1：微信 JSAPI，2：微信扫码C扫B，3：竖屏B扫C，8：储蓄卡支付，10：APP微信支付，11：app支付宝支付，12：B扫C（标准）
        // 13:微信 JSAPI  14:微信扫码C扫B  15:支付宝扫码C扫B 16：网银支付 17：标准支付宝小程序支付 18：百度小程序支付
        $payment_code = '';
        switch ($data['trans_type']) {
            case 17:
                $payment_code = 'ali_native';
                break;
            case 18:
                $payment_code = 'bd_pay';
                break;
        }

        if ($payment_code != '') {
            $update = array('payment_code' => $payment_code);
            if (isset($data['is_virtual']) && $data['is_virtual']) { // 虚拟
                $res = VrOrder::where('order_sn', $data['order_sn'])->update($update);
            } else {
                $res = Order::where('order_sn', $data['order_sn'])->update($update);
            }
            output_data($res, array('data' => $data, 'update' => $update, 'payment_code' => $payment_code));
        }

        output_data("未操作");
    }


    /**
     * 接收数据中心拼团成功订单
     */
    public function receiveGroupOrderInfoOp()
    {
        $receive_data = file_get_contents('php://input');
        $order_info = json_decode($receive_data, true); ////stripslashes
        $log = [
            C('mobile_site_url') . '/index.php?act=openapi&op=receiveGroupOrderInfo',
            addslashes($receive_data),
            addslashes($receive_data),
            "接收拼团成功成功订单数据",
            $order_info['order_sn'],
            5
        ];
        Model('member')->addApiRequestLog(...$log);
        if (is_array($order_info) && !empty($order_info)) {
            $sign = $order_info['sign'];
            unset($order_info['sign']);

            $lock = Redis::lock("receiveGroupOrderInfo:$sign", 30)
                ->setAutoRelease();

            if (!$lock->get()) {
                output_error('请求处理中');
            }

            $sure_sign = $this->getSignature($order_info);
            if ($sure_sign == $sign) {
                $model_member = Model('member');
                $business_json = json_decode($order_info['business_json'], true);
                $buyer_phone = $business_json['buyer_phone'];
                $field = "member_id,member_name,member_email,member_mobile,member_isvip,member_isbzk";
                $member_info = $model_member->getMemberInfo(['member_mobile' => $buyer_phone], $field);
                // 标记拼团，拼团不允许赠品
                $_POST['pintuan_receive'] = 1;

                if ($order_info['is_virtual'] == 1) { //虚拟订单
                    $logic_buy_virtual = Logic('buy_virtual');
                    $input = array();
                    $input['goods_id']      = $order_info['goods_id'];
                    $input['quantity']      = $order_info['goods_num'];
                    $input['buyer_phone']   = $buyer_phone;
                    $input['buyer_msg']     = $business_json['buyer_msg'];
                    $input['chain_id']      = $business_json['chain_id'];
                    $input['pet_info']      = $business_json['pet_info'];
                    $input['insure_info']   = $business_json['insure_info'];
                    $input['pintuan']       = 2;
                    $input['voucher']       = $business_json['voucher'];
                    $input['order_from']    = $business_json['order_from'];
                    $input['dis_type']      = $business_json['dis_type'] ? $business_json['dis_type'] : 0;
                    $input['dis_id']        = $business_json['dis_id'] ? $business_json['dis_id'] : 0;
                    $input['is_live']       = $business_json['is_live'] ? intval($business_json['is_live']) : 0;
                    $input['first_order']   = $business_json['first_order'] ? intval($business_json['first_order']) : 0;
                    $input['first_order']   = $business_json['first_order'] ? intval($business_json['first_order']) : 0;
                    $input['trade_no']      = $order_info['trade_no'];
                    $input['payment_time']  = strtotime($order_info['payment_time']) > 0 ? strtotime($order_info['payment_time']) : time();
                    $input['add_time']      = strtotime($order_info['add_time']);
                    $input['order_sn']      = $order_info['order_sn'];
                    $input['pin_info'] = [
                        'goods_old_price' => ncPriceFormat(($order_info['goods_price'] / 100)),
                        'goods_total' => ncPriceFormat(($order_info['goods_pay_price'] / 100) * $order_info['goods_num']),
                        'goods_price' => ncPriceFormat(($order_info['goods_pay_price'] / 100)),
                        'begin_date' => $business_json['start_time'],
                        'end_date' => $business_json['end_time'],
                        'gid' => $business_json['gid'],
                        'success_num' => $business_json['max_participant_number'],
                        'part_num' => $business_json['max_number'],
                        'price' => ncPriceFormat(($business_json['price']))
                    ];
                    $input['pay_type'] = $order_info['pay_mode'];
                    $input['ignore_push'] = $order_info['ignore_push'];

                    $result = $logic_buy_virtual->buyStep3($input, $member_info['member_id']);
                    if (!$result['state']) {
                        output_error($result['msg']);
                    } else {
                        // 同步新增的支付宝支付和百度支付
                        $this->syncPinPaymentCode($order_info);

                        output_data("1");
                    }
                } else { // 实物订单
                    $member_level = 0;
                    if ($member_info['member_isvip'] > 0 || $member_info['member_isbzk'] > 0) {
                        $member_level = 1;
                    }
                    $member_info['level'] = $member_level; //$member_gradeinfo['level'];
                    //读取卖家信息
                    $seller_info = Model('seller')->getSellerInfo(array('member_id' => $member_info['member_id']));
                    $member_info['store_id'] = $seller_info['store_id'];
                    /** @var buyLogic $logic_buy */
                    $logic_buy = logic('buy');
                    $param = array();
                    $param['ifcart'] = $_POST['ifcart'];
                    $param['cart_id'] = explode(',', $business_json['cart_id']);
                    $param['address_id'] = $business_json['address_id'];
                    $param['input_address_info'] = $business_json['address_info'];
                    $param['vat_hash'] = $_POST['vat_hash'];
                    $param['offpay_hash'] = $_POST['offpay_hash'];
                    $param['offpay_hash_batch'] = $_POST['offpay_hash_batch'];
                    $param['pay_name'] = $business_json['pay_name'];
                    $param['invoice_id'] = $_POST['invoice_id'];
                    $param['rpt'] = $_POST['rpt'];
                    $param['chain_id'] = $business_json['chain_id']; //门店id
                    $param['power_state'] = intval($business_json['power_state']); //是否原价购买
                    $param['pintuan'] = $_POST['pintuan'] = 2;
                    $param['pin_type'] = $_POST['pin_type'] = $business_json['pin_type'];
                    $param['gid'] = $_POST['gid'] = intval($business_json['gid']);
                    //得到购买商品信息
                    if ($_POST['ifcart']) {
                        $result = $logic_buy->getCartList($param['cart_id'], $member_info['member_id'], null, null);
                    } else {
                        $result = $logic_buy->getGoodsList($param['cart_id'], $member_info['member_id'], $member_info['store_id']);
                    }

                    if (!$result['state']) {
                        output_error($result['msg']);
                    }

                    $param['voucher'] = $business_json['voucher'];
                    $_POST['pay_message'] = trim($business_json['pay_message'], ',');
                    $_POST['pay_message'] = explode(',', $_POST['pay_message']);
                    $param['pay_message'] = array();
                    if (is_array($_POST['pay_message']) && $_POST['pay_message']) {
                        foreach ($_POST['pay_message'] as $v) {
                            if (strpos($v, '|') !== false) {
                                $v = explode('|', $v);
                                $param['pay_message'][$v[0]] = $v[1];
                            }
                        }
                    }
                    $param['pd_pay'] = $_POST['pd_pay'];
                    $param['rcb_pay'] = $_POST['rcb_pay'];
                    $param['password'] = $_POST['password'];
                    $param['fcode'] = $_POST['fcode'];
                    $param['order_from'] = $business_json['order_from'];
                    $param['power_id'] = intval($business_json['power_id']) ? $business_json($_POST['power_id']) : 0;
                    $param['dis_type'] = $business_json['dis_type'] ? $business_json['dis_type'] : 0;
                    $param['is_live'] = $business_json['is_live'] ? intval($business_json['is_live']) : 0;
                    $param['first_order'] = $business_json['first_order'] ? intval($business_json['first_order']) : 0;
                    $param['trade_no']      = $order_info['trade_no'];
                    $param['payment_time']  = strtotime($order_info['payment_time']) > 0 ? strtotime($order_info['payment_time']) : time();
                    $param['add_time']      = strtotime($order_info['add_time']);
                    $param['order_sn']      = $order_info['order_sn'];
                    $param['pay_sn']      = $order_info['order_sn']; // 拼团订单支付单号等于父单号
                    $param['pin_info'] = [
                        'goods_old_price' => ncPriceFormat(($order_info['goods_price'] / 100)),
                        'goods_total' => ncPriceFormat(($order_info['goods_pay_price'] / 100) * $order_info['goods_num']),
                        'goods_price' => ncPriceFormat(($order_info['goods_pay_price'] / 100)),
                        'price' => $order_info['goods_pay_price'],
                        'store_freight_total' => ncPriceFormat(($business_json['store_freight_total'] / 100)),
                    ];
                    $param['pay_type'] = $order_info['pay_mode'];
                    $param['ignore_push'] = $order_info['ignore_push'];
                    $param['source'] = isset($business_json['source']) ? $business_json['source'] : 3;
                    $param['order_amount'] = ncPriceFormat(($order_info['order_amount'] / 100));
                    $logic_buy->setMemberInfo($member_info);
                    $result = $logic_buy->buyStep2($param, $member_info);
                    if (!$result['state']) {
                        output_error($result['msg']);
                    }

                    // 触发实物订单创建事件，处理卡券、商家券核销
                    event(new OrderCreated($result['data']['order_id']));

                    // 同步新增的支付宝支付和百度支付
                    $this->syncPinPaymentCode($order_info);

                    output_data("1");
                }
            } else {
                output_error("签名错误");
            }
        } else {
            output_error("解析数据失败");
        }
    }

    // 修复拼团订单默认微信支付，同步新增的支付宝支付和百度支付
    function syncPinPaymentCode($order_info)
    {
        $payMode = array(1 => 'ali_native', 18 => 'bd_pay');
        if (isset($payMode[$order_info['pay_mode']]) && $payCode = $payMode[$order_info['pay_mode']]) {
            if ($order_info['is_virtual'] == 1) {
                VrOrder::where('order_sn', $order_info['order_sn'])->update(array('payment_code' => $payCode));
            } else {
                Order::where('order_sn', $order_info['order_sn'])->update(array('payment_code' => $payCode));
            }
        }
    }


    /**
     * 更新虚拟商品库存
     */
    public function freezedVirtualStockOp()
    {
        $receive_data = file_get_contents('php://input');
        $order_info = json_decode($receive_data, true);
        if (is_array($order_info) && !empty($order_info)) {
            $sign = $order_info['sign'];
            unset($order_info['sign']);
            $sure_sign = $this->getSignature($order_info);
            if ($sure_sign == $sign) {
                $order_sn = $order_info['order_sn'];
                if (!$order_sn) {
                    output_error("订单不存在");
                }
                $model_goods = Model('goods');
                $field = "ps_goods_id,voucher_ids,ps_status,ps_goods_num,ps_id,member_id";
                $pin_stocklog_info = $model_goods->getPinStocklogInfo(['ps_order_sn' => $order_sn, 'ps_status' => 0], $field);
                if (!empty($pin_stocklog_info) && $pin_stocklog_info['voucher_ids']) {
                    //                    $voucher = explode('|',$pin_stocklog_info['voucher_ids']);
                    $voucher = json_decode($pin_stocklog_info['voucher_ids'], true);
                    $res = Model('voucher')->pinStockVoucher($voucher, $pin_stocklog_info['member_id']);
                    if (!$res) {
                        output_error("优惠券更新失败");
                    }
                    $model_goods->editPinStocklog(['ps_id' => $pin_stocklog_info['ps_id']], ['ps_status' => 1, 'ps_updatetime' => time()]);
                    output_data("1");
                } elseif (!empty($pin_stocklog_info) && $pin_stocklog_info['ps_goods_num']) {
                    $storage_res = $model_goods->table('goods')->where(["goods_id" => $pin_stocklog_info['ps_goods_id']])->setInc('goods_storage', $pin_stocklog_info['ps_goods_num']);
                    if ($storage_res) {
                        $model_goods->editPinStocklog(['ps_id' => $pin_stocklog_info['ps_id']], ['ps_status' => 1, 'ps_updatetime' => time()]);
                        output_data("1");
                    }
                }
            } else {
                output_error("签名错误");
            }
        } else {
            output_error("解析数据失败");
        }
    }

    /**
     * 小程序消息推送
     * https://developers.weixin.qq.com/miniprogram/dev/framework/ministore/minishopopencomponent2/callback/spu_audit.html
     */
    public function miniProgramMessagePushOp()
    {
        $input = file_get_contents('php://input');
        $input = json_decode($input, true);
        $sign = $input['sign'];

        if (empty($input['data']) || empty($sign)) {
            output_error('参数错误');
        }
        unset($input['sign']);
        if ($sign <> $this->getSignature($input)) {
            output_error('签名错误');
        }

        $data = json_decode($input['data'], true);

        try {
            SyncTask::createAndRun(
                $data,
                'mini_program',
                $data['Event'] . '_callback'
            );
        } catch (Exception $exception) {
            log_error('miniProgramMessagePush:' . $exception->getMessage(), $data);
        }

        output_data(1);
    }

    /**
     * 获取签名
     *
     * @return string
     */
    private function getSignature($param)
    {
        ksort($param);
        $string = '';
        foreach ($param as $key => $val) {
            if ($val !== "") {
                $string .= $key . '=' . $val . '&';
            }
        }
        $string = rtrim($string, "&");
        $string = md5($string) . C('openapi_key');
        return strtoupper(md5($string));
    }

    /**
     * 查询对应优惠券规则
     */
    public function getVoucherOp()
    {
        $con_type = $_POST['con_type']; //1首页弹窗，2支付领取
        if (empty($con_type)) {
            output_data(['show' => false, 'message' => '类型不能为空']);
        }

        $member_info = $this->getMemberIfExists();
        if (empty($member_info)) {
            output_data(['show' => false, 'message' => '用户信息错误']);
        }

        $type_test = '';
        switch ($con_type) {
            case 1:
                $type_test = 'Index';
                $order_main = OrderMain::where(['member_id' => $member_info['scrm_user_id']])->where('order_status', '>=', 20)->find();

                $coupon_info = CouponActivity::where(['ca_business_type' => 7])
                    ->where(['ca_coupon_type' => 1])
                    ->where('ca_status', 'in', [1, 2])
                    ->where('ca_start_date', '<', time())
                    ->where('ca_end_date', '>', time());
                if ($order_main) {
                    $type = 4; //首页弹窗
                    $type_test .= 'oldMember';
                    $coupon_info = $coupon_info->where(['ca_coupon_scene' => 4]);
                } else {
                    $type = 3; //首页弹窗
                    $type_test .= 'newMember';
                    $coupon_info = $coupon_info->where(['ca_coupon_scene' => 3]);
                }
                $coupon_info = $coupon_info->order('ca_create_date desc')->find();
                if (Redis::get($member_info['member_id'] . $type_test)) {

                    if (C('pro') == 'dev') {

                        output_data(['show' => false, 'message' => '今天已经触发过了']);
                    }
                }
                break;
            case 2:
                $type = 5; //支付
                $type_test = 'Pay';
                /*  if(RedisManager::get($member_info['member_id'].$type_test)){

                    if(C('pro')=='dev'){

                        output_data(['show'=>false,'message'=>'今天已经触发过了']);
                    }
                }*/
                $coupon_info = CouponActivity::where(['ca_business_type' => 5])
                    ->where(['ca_coupon_type' => 1])
                    ->where('ca_status', 'in', [1, 2])
                    ->where('ca_start_date', '<', time())
                    ->where('ca_end_date', '>', time())
                    ->where(['ca_coupon_scene' => 2])
                    ->order('ca_create_date desc')
                    ->find();
                break;
            default:
        }
        if ($coupon_info) {
            $this->addVoucherList($coupon_info, $member_info, $type, $type_test);
        } else {
            output_data(['show' => false, 'message' => '没有相关活动']);
        }
    }

    //批量兑换优惠券 商城券
    public function addVoucherList($coupon_info, $member_info, $type, $type_test)
    {
        $model_voucher = Model('voucher');
        $voucher_list = CouponActivityVoucher::where(['ca_id' => $coupon_info['id']])->select();

        //读取卖家信息
        $seller_info = Model('seller')->getSellerInfo(array('member_id' => $member_info['member_id']));

        $store_id = $seller_info['store_id'];
        //验证是否可领取代金券
        $data_list = [];
        $msg = '优惠卷已领取';

        foreach ($voucher_list as $val) {
            $data = $model_voucher->getCanChangeTemplateInfo($val['ca_coupon_id'], $member_info['member_id'], $store_id);

            if ($data['state']) {
                $data_list[] = $data['info'];
            } else {
                $msg = $data['msg'];
            }
        }
        if (empty($data_list)) {
            output_data(['show' => false, 'message' => $msg]);
        }

        try {
            $model_voucher->beginTransaction();
            //添加代金券信息
            $data = $model_voucher->exchangeVoucherList($data_list, $member_info['member_id'], $member_info['member_name'], $type);
            if ($data['state'] == false) {
                throw new Exception($data['msg']);
            }
            $model_voucher->commit();
            $expireTime = strtotime('tomorrow') - 1 - time();

            //设置缓存时间
            Redis::set($member_info['member_id'] . $type_test, $member_info['member_id'], $expireTime);
            $coup_data = ['ca_numbers' => $coupon_info['ca_numbers'] + count($data_list)];
            if ($coupon_info['ca_status'] == 1) {
                //如果未开始，就变为开始
                $coup_data['ca_status'] = 2;
            }

            CouponActivity::update($coup_data, ['id' => $coupon_info['id']]);
            output_data(['show' => true, 'voucher' => $data_list, 'img_url' => $coupon_info['ca_img_url'], 'wx_path' => $coupon_info['ca_path']]);
        } catch (Exception $e) {
            $model_voucher->rollback();
            output_error($e->getMessage());
        }
    }
    /**
     * 查询对应优惠券规则
     */
    public function isCheckVoucherOp()
    {
        $scrmId = $this->getScrmUserId();

        if (!$scrmId) {
            output_data(['show' => false, 'message' => '用户信息错误']);
        }

        $order_main = OrderMain::where(['member_id' => $scrmId])->where('order_status', '>=', 20)->find();

        if ($order_main) {
            output_data(['show' => false, 'message' => '老用户']);
        } else {
            output_data(['show' => true, 'message' => '新用户']);
        }
    }

    /**
     * 根据优惠券模板名称查找，支持模糊匹配
     * 只返回可以使用优惠券
     */
    public function voucherSearchOp()
    {
        $keyword = is_null($_GET['keyword']) ? '' : $_GET['keyword'];
        $page = isset($_GET['page']) && $_GET['page'] > 0 ? intval($_GET['page']) : 1;
        $page_size = isset($_GET['page_size']) && $_GET['page_size'] > 0 ? intval($_GET['page_size']) : 10;
        if ($page_size > 50) $page_size = 50;  // 限制最多50个

        // A、签名校验
        $sign = trim($_GET['sign']);
        $sure_sign = $this->getSignature(['keyword' => $keyword]);
        if ($sure_sign != $sign) {
            output_error("无效请求");
        }

        // B、优惠券条件组装,查询
        $model = new VoucherTemplate();
        $field = 'voucher_t_title, voucher_t_id, voucher_t_start_date, voucher_t_end_date, voucher_start_day, voucher_days';
        $model = $model->field($field)->where('voucher_t_state', '=', 1);   // 默认条件，1-有效,2-失效
        if ($keyword) {
            $model = $model->whereLike('voucher_t_title', "%$keyword%");
        }

        $model_count = clone $model;
        $voucher_list = $model->limit(($page - 1) * $page_size, $page_size)->order('voucher_t_id desc')->select();
        $page_count = $model_count->count();

        // C、结果集拼接返回
        $data = array();
        if (count($voucher_list) > 0) {
            foreach ($voucher_list as $val) {
                $data['voucher_list'][] = array(
                    'voucher_t_title' => $val['voucher_t_title'],
                    'voucher_t_id' => $val['voucher_t_id'],
                    'voucher_start_day' => $val['voucher_start_day'],
                    'voucher_days' => $val['voucher_days'],
                    'voucher_t_start_date_text' => $val['voucher_t_start_date'] > 0 ? date('Y-m-d H:i:s', $val['voucher_t_start_date']) : '',
                    'voucher_t_end_date_text' => $val['voucher_t_end_date'] > 0 ? date('Y-m-d H:i:s', $val['voucher_t_end_date']) : '',
                );
            }
        }

        output_data($data, array_merge(mobile_page(ceil($page_count / $page_size)), ['total' => $page_count]));
    }

    // 临时处理水印图片
    public function goodsInfoOp()
    {
        $data = [];
        if ($_GET['pwd'] == 'upetmart888') {
            // 更新操作
            if ($goods_id = intval($_GET['goods_id'])) {
                if ($goods_image = $_GET['goods_image']) {
                    $goods_image = urldecode($goods_image);
                    Goods::where('goods_id', $goods_id)->where('store_id', 1)->update(['goods_image' => $goods_image]);
                    echo json_encode(['code' => 200, 'msg' => 'succees']);
                    die;
                }
                echo json_encode(['code' => 400, 'msg' => '图片地址不存在！']);
                die;
            }


            // 查询操作
            $str_goods_ids = $_GET['goods_ids'];
            if ($str_goods_ids) {
                $result = Goods::whereRaw(' goods_id in (' . $str_goods_ids . ')')->where('store_id', 1)->select();
                if (count($result) > 0) {
                    foreach ($result as $val) {
                        $data[intval($val['goods_id'])] = [
                            'goods_price' => $val['goods_price'],
                            'goods_image' => $val['goods_image'],
                        ];
                    }
                }
            }
        }
        echo json_encode($data, JSON_UNESCAPED_UNICODE);
        die;
    }

    // 临时处理水印图片
    public function goodsListOp()
    {
        $data = [];
        if ($_GET['pwd'] == 'upetmart888') {
            // 查询操作
            $str_goods_ids = $_GET['goods_ids'];
            if ($str_goods_ids) {
                $data = Goods::whereRaw(' goods_id in (' . $str_goods_ids . ')')->select();
            }
            foreach ($data as $key => $val) {
                $data[$key]['goods_price'] = (float)$val['goods_price'];
                $data[$key]['goods_promotion_price'] = (float)$val['goods_promotion_price'];
                $data[$key]['goods_marketprice'] = (float)$val['goods_marketprice'];
                $data[$key]['goods_freight'] = (float)$val['goods_freight'];
                $data[$key]['book_down_payment'] = (float)$val['book_down_payment'];
                $data[$key]['book_final_payment'] = (float)$val['book_final_payment'];
                $data[$key]['goods_trans_v'] = (float)$val['goods_trans_v'];
                $data[$key]['member_price_1'] = (float)$val['member_price_1'];
                $data[$key]['member_price_2'] = (float)$val['member_price_2'];
                $data[$key]['member_price_3'] = (float)$val['member_price_3'];
                $data[$key]['member_price_3'] = (float)$val['member_price_3'];

                $data[$key]['contract_1'] = (bool)$val['contract_1'];
                $data[$key]['contract_2'] = (bool)$val['contract_2'];
                $data[$key]['contract_3'] = (bool)$val['contract_3'];
                $data[$key]['contract_4'] = (bool)$val['contract_4'];
                $data[$key]['contract_5'] = (bool)$val['contract_5'];
                $data[$key]['contract_6'] = (bool)$val['contract_6'];
                $data[$key]['contract_7'] = (bool)$val['contract_7'];
                $data[$key]['contract_8'] = (bool)$val['contract_8'];
                $data[$key]['contract_9'] = (bool)$val['contract_9'];
                $data[$key]['contract_10'] = (bool)$val['contract_10'];
                $data[$key]['is_chain'] = (bool)$val['is_chain'];
                $data[$key]['is_batch'] = (bool)$val['is_batch'];
                $data[$key]['g_search_status'] = (bool)$val['g_search_status'];
                $data[$key]['is_vip'] = (bool)$val['is_vip'];
                $data[$key]['is_bzk'] = (bool)$val['is_bzk'];
            }
        }
        echo json_encode($data, JSON_UNESCAPED_UNICODE);
        die;
    }

    /**
     * 获取商品标签
     */
    public function tagsOp()
    {
        header("Content-type: application/json; charset=UTF-8");

        $query = Tag::field('tag_id as id,tag_name as name')
            ->order('tag_sort desc,tag_id asc');

        if ($keyword = $_GET['keyword']) {
            $query->whereLike('tag_name', "%$keyword%");
        }

        $tags = $query->select();

        exit(json_encode([
            'data' => $tags,
            'success' => true
        ], JSON_UNESCAPED_UNICODE));
    }

    /**
     * 获取新人专享标签
     * http://**************:8089/web/#/2?page_id=295
     */
    public function newcomer_tagOp()
    {
        $user_id = $_GET['user_id'];
        $sign = $_GET['sign'];

        if (empty($user_id) || empty($sign)) {
            output_error("参数错误");
        }
        if ($sign != $this->getSignature(['user_id' => $user_id])) {
            output_error("签名错误");
        }

        $member = Model('member')->getMemberInfo(['scrm_user_id' => $user_id]);
        $tag = action(new GetNewcomerTagAction($member, false, false));
        $unUseSkuId = 0;

        // 非新用户查找未核销的新人套餐
        if ($tag <> MemberAlias::NEWCOMER) {
            $skuId = VrOrder::where('buyer_id', $member['member_id'])
                ->where('order_type', 10)
                ->whereIn('order_state', [10, 20]) // 未核销
                ->value('goods_id');
            // 如果存在未核销的套餐，则状态为2，否则为老用户
            if ($skuId) {
                $unUseSkuId = (int)$skuId;
                $tag = MemberAlias::NEWCOMER_ORDER;
            } else {
                $tag = MemberAlias::NEWCOMER_OLD;
            }
        }

        // 如果是新人下单，实物订单当成老人
        if ($tag == MemberAlias::NEWCOMER_ORDER) {
            $exists = Order::where('buyer_id', $member['member_id'])
                ->where('order_type', 10)
                ->where('order_state', '>', 0)
                ->value('order_id');

            if ($exists) {
                $tag = MemberAlias::NEWCOMER_OLD;
            }
        }

        output_data([
            'newcomer_tag' => (int)$tag,
            'un_use_sku_id' => $unUseSkuId
        ]);
    }

    /**
     * Notes:会员卡申请售后
     * Desc:提供阿闻调用
     * param:order_sn 订单号
     * param:buyer_message 申请原因
     * param:apply_type 申请类型 1:仅注销身份和权益 2:退款+注销身份和权益
     * param:apply_user 申请人
     * User: rocky
     * DateTime: 2023/8/8 15:03
     */
    public function vrOrderAppayOp()
    {
        $dataJson = stripslashes(html_entity_decode($_POST['data']));
        $list = $this->interfaceVerify('vrOrderAppay');
        if (!$list['order_sn'] || !$list['buyer_message'] || !$list['apply_user']) {
            output_error("请求参数错误");
        }
        $order_info = Model()->table('vr_order,member')->join('left')->on('vr_order.buyer_id = member.member_id')
            ->where(['erp_order_sn' => $list['order_sn']])->field('vr_order.order_id,vr_order.buyer_id,member.scrm_user_id')->find();
        if (!$order_info) {
            output_error("订单号有误");
        }
        Model('vr_refund')->vrRefundApply([
            'member_id' => $order_info['buyer_id'],
            'user_id' => $order_info['scrm_user_id'],
            'order_id' => $order_info['order_id'],
            'apply_user' => $list['apply_user'],
            'buyer_message' => $list['buyer_message'],
            'apply_type' => $list['apply_type']
        ]);
        $log = [
            C('mobile_site_url') . '/index.php?act=openapi&op=vrOrderAppay',
            null,
            $dataJson,
            "接收会员卡申请售后",
            $list['order_sn'],
            5
        ];
        Model('member')->addApiRequestLog(...$log);

        output_data("1");
    }

    /**
     * Notes:会员卡售后审核
     * param:refund_id 退款id
     * param:refund_amount 退款金额(分)
     * param:admin_state 审核状态:1为待审核,2为同意,3为不同意
     * param:admin_message 备注
     * param:admin_user 操作人
     * param:admin_type 审核类型：0-默认人工 1-系统自动
     * User: rocky
     * DateTime: 2023/8/8 22:49
     */
    public function vrOrderRefundOp()
    {
        $result = array('state' => 'false', 'msg' => '参数错误，微信退款失败');
        $list = $this->interfaceVerify('vrOrderRefund');
        if ((isset($list['refund_id']) && !$list['refund_id']) || !$list['admin_user']) {
            output_error("请求参数错误");
        }
        $refund_id = intval($list['refund_id']);
        $model_refund = Model('vr_refund');
        $refund = $model_refund->getRefundInfo(['refund_id' => $refund_id]);
        $detail_array = $model_refund->getDetailInfo(['refund_id' => $refund_id, 'refund_state' => 1]);
        $order = $model_refund->getPayDetailInfo($detail_array); //退款订单详细

        /** @var refundLogic $refundLogic */
        $refundLogic = Logic('refund');

        //不退款只审核操作
        if ($refund['apply_type'] == 1 || $list['admin_state'] == 3) {
            if ($refund['admin_state'] != '1') { //检查状态,防止页面刷新不及时造成数据错误
                output_error('非审核状态，无法操作');
            }
            if ($detail_array['pay_time'] > 0) {
                $refund['pay_amount'] = $detail_array['pay_amount']; //已完成在线退款金额
            }
            $refund['admin_time'] = time();
            $refund['admin_state'] = '2';
            if ($list['admin_state'] == '3') {
                $refund['admin_state'] = '3';
            }
            $refund['admin_message'] = sprintf('%s %s', $list['admin_message'], '仅注销身份');
            $refund['operationer'] = $list['admin_user'];
            $refund['admin_user'] = $list['admin_user'];
            $refund['mobile'] = $order['buyer_phone'];
            $refund['auto_status'] = $list['admin_type'];

            //退款ERP虚拟订单
            $res = $refundLogic->syncBatchVrOrderRefund($refund, $order);
            if (!$res) {
                output_error("操作申请失败，您稍后再申请~~");
            }

            unset($refund['operationer']);

            $state = $model_refund->editOrderRefund($refund);
            if ($state) {
                $result['state'] = 'true';
                $result['msg'] = '操作成功';
            } else {
                output_error('保存失败');
            }
        } else {
            $order = $model_refund->getPayDetailInfo($detail_array); //退款订单详细
            $pay_refund_amount = $order['pay_refund_amount']; //本次在线退款总金额
            $refund_amount = ncPriceFormat($list['refund_amount'] / 100);
            if ($refund_amount > $pay_refund_amount) {
                output_error('退款金额输入有误');
            }
            if ($refund_amount > 0) {
                if ((C('dianyin_pay') && $order['payment_from'] > 0) || $order['payment_from'] > 0) {
                    $use_refund_time = $order['payment_time'] + C('dianyin_allow_time');
                    if ($order['payment_code'] <> 'card' && $use_refund_time > time()) {
                        $result['msg'] = '操作太快了，5分钟后再来吧~';
                    } else {
                        $refund = $model_refund->getRefundInfo(array('refund_id' => $refund_id));

                        $refund_data = array();
                        $refund_data['trade_no'] = $order['trade_no'];
                        $refund_data['refund_amount'] = $refund_amount;
                        $refund_data['order_type'] = $order['order_type'];
                        $refund_data['payment_code'] = $order['payment_code'];
                        $refund_data['refund_sn'] = $refund['refund_sn'];
                        /**@var dianyin_payLogic $dianyinLogic */
                        $dianyinLogic = Logic('dianyin_pay');
                        $dianyin_result = $dianyinLogic->orderRefund($refund_data);

                        $data = $dianyin_result['data'];
                        if ($dianyin_result['state'] && in_array($data['rspCode'], ['1', '2', '0'])) {
                            $detail_array = array();
                            $detail_array['pay_amount'] = ncPriceFormat($data['refundAmt'] / 100);
                            $detail_array['pay_time'] = time();
                            $model_refund->editDetail(array('refund_id' => $refund_id), $detail_array);
                            $result['state'] = 'true';
                            $result['msg'] = '已提交退款申请:' . $detail_array['pay_amount'];

                            $consume_array = array();
                            $consume_array['member_id'] = $refund['buyer_id'];
                            $consume_array['member_name'] = $refund['buyer_name'];
                            $consume_array['consume_amount'] = $detail_array['pay_amount'];
                            $consume_array['consume_time'] = time();
                            $consume_array['consume_remark'] = '在线退款成功（到账有延迟），虚拟退款单号：' . $refund['refund_sn'];
                            QueueClient::push('addConsume', $consume_array); //old end

                            $refund['admin_state_virbalance'] = 3;
                            $refund['admin_time'] = time();
                            $refund['admin_state'] = '2';
                            $refund['admin_message'] = $list['admin_message'];
                            $refund['dy_state'] = 1;
                            $refund['admin_user'] = $list['admin_user'];
                            $refund['auto_status'] = $list['admin_type'];
                            $refund['refund_amount'] = $detail_array['pay_amount'];
                            if ($data['rspCode'] == '1' || $data['rspCode'] == '2') {
                                $refund['dy_state'] = 2;
                                $refund['dy_dealtime'] = time();
                            }

                            $refund['dy_refund_id'] = $data['refundId'];
                            $refund['dy_transaction_no'] = $data['transactionNo'];
                            $state = $model_refund->editOrderRefund($refund);
                            if ($state) {
                                $refund['msg'] = '管理员已处理退款，请查收'; //状态描述
                                $refund['mobile'] = $order['buyer_phone'];

                                //退款ERP虚拟订单
                                $refundLogic->syncBatchVrOrderRefund($refund, $order);
                            }
                        } else {
                            if ($data['rspCode'] == "3" || $data['rspCode'] == "0" || $data['rspCode'] == "-1") {
                                $result['msg'] = '退款返回信息,' . $data['msg']; //错误描述
                            } else {
                                $result['msg'] = '退款错误,' . $dianyin_result['msg']; //错误描述
                            }
                        }
                    }
                }
            }
        }

        if ($result['state'] == 'true') {
            // 发票红冲操作
            SyncInvoiceInfoQueue::dispatch($refund_id);
            output_data("1");
        } else {
            output_error($result['msg']);
        }
    }

    /**
     * Notes:实体卡完成
     * User: rocky
     * DateTime: 2023/9/7 9:19
     */
    public function ordercompletedOp()
    {
        $list = $this->interfaceVerify('ordercompleted');
        $model_order = Model('order');
        $condition = array();
        $condition['order_sn'] = $list['order_sn'];
        $order_info = $model_order->getOrderInfo($condition);

        if (!in_array($order_info['order_state'], [10, 20, 30])) {
            output_error("订单状态不是待完成审核");
        }
        /** @var orderLogic $logic_order */
        $logic_order = Logic('order');
        $result = $logic_order->changeOrderStateReceive($order_info, 'admin', 'admin', '会员卡已激活');
        if (!$result['state']) {
            output_error($result['msg']);
        } else {
            output_data('1');
        }
    }

    public function interfaceVerify($key)
    {
        $data = $_POST['data'];
        if (empty($data)) {
            output_error("请求data不能为空");
        }
        $sign = trim($_POST['sign']);
        $dataJson = stripslashes(html_entity_decode($data));
        $sure_sign = $this->getSignature(['data' => $dataJson]);
        if ($sure_sign != $sign) {
            output_error("签名错误");
        }
        $md5_key = md5($dataJson);
        $lock = Redis::lock("$key:$md5_key", 30)->setAutoRelease();
        if (!$lock->get()) {
            output_error('请求处理中');
        }
        return json_decode($dataJson, true);
    }
}
