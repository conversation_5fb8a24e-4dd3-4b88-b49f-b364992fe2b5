<?php

/**
 * 购买行为
 *
 *
 *
 *
 * @copyright  Copyright (c) 2007-2018 ShopNC Inc. (http://www.shopnc.net)
 * @license    http://www.shopnc.net
 * @link       http://www.shopnc.net
 * @since      File available since Release v1.1
 */
defined('InShopNC') or exit('Access Invalid!');

use Upet\Exceptions\SubmitStockException;
use Upet\Integrates\Redis\RedisManager as Redis;
use Upet\Models\BookBuyProduct;
use Upet\Models\Datacenter\VipCardGift;
use Upet\Models\DcActivity\NewBuyProduct;
use Upet\Models\DcActivity\PromotionProduct;
use Upet\Models\DcCustomer\RiskUser;
use Upet\Models\DcCustomer\RiskWhitelist;
use Upet\Models\DcCycleBuyProduct;
use Upet\Models\DcGroupBuyProduct;
use Upet\Models\OrderMain;
use Upet\Models\DcOrder\OrderDetail;
use Upet\Models\GoodsGroup;
use Upet\Models\Member as MemberAlias;
use Upet\Models\Order as OrderAlias;
use Upet\Models\OrderPresale;
use Upet\Models\Shr\ShrTStaffInfo;
use Upet\Models\SyncTask;
use Upet\Modules\Goods\Queues\SyncUpdateGoodsStorageQueue;
use Upet\Modules\Member\Queues\SyncDisMemberQueue;
use Upet\Modules\Order\Actions\MixTransformToMiniShopAction;
use Upet\Modules\Order\Queues\SyncDatacenterOrderPayQueue;
use Upet\Modules\Member\Queues\SyncDisMemberFansQueue;
use Upet\Models\Eshop\DisDistributor;
use Upet\Modules\Member\Queues\SyncDisDistributorFansOrder;
use Upet\Models\Blky\Xkucun;
use Upet\Modules\Goods\Queues\OrderSettQueue;
use Upet\Models\OrderProduct;
use Upet\Modules\Goods\Queues\SyncCreateOrderQueue;
use Upet\Models\Goods;
use Upet\Models\Blky\SyncOutboundLog;
use Upet\Models\Blky\SjanOutboundDetails;
use Upet\Models\Blky\SyncUnUbarcode;
use Upet\Models\Eshop\DisCustomerChannel;
use Upet\Models\Eshop\DisEnterprise;
use Upet\Models\Eshop\Shop;
use Upet\Models\Syncdb\WarehouseSalesOutstock;
use Upet\Models\Eshop\ScrmEnterprise;
use Upet\Models\Blky\XlogisticsDetail;

class buyLogic
{

    /**
     * 会员信息
     * @var array
     */
    private $_member_info = array();

    /**
     * 下单数据
     * @var array
     */
    private $_order_data = array();

    /**
     * 表单数据
     * @var array
     */
    private $_post_data = array();

    /**
     * buy_1.logic 对象
     * @var buy_1Logic
     */
    private $_logic_buy_1;

    /**
     * 虚拟库存redis扣减数据
     *
     * @var array
     */
    protected $virtualStockDecrBy = [];

    /**
     * 新人专享信息
     *
     * @var array
     */
    protected $newcomerInfo = [];

    /**
     * 预售信息
     *
     * @var array
     */
    protected $bookBuyInfo = [];

    public function __construct()
    {
        $this->_logic_buy_1 = Logic('buy_1');
    }

    /**
     * 是否是互联网医院
     * @return bool
     */
    public function isHospital()
    {
        return (isset($_POST['from_scene']) && $_POST['from_scene'] == 'hospital');
    }

    // 互联网医院咨询单号，存在则是互联网医院处方订单
    public function isExsitHospitalConsultOrderSn()
    {
        return (isset($_POST['consult_order_sn']) ? $_POST['consult_order_sn'] : '');
    }

    /**
     * 会员信息buyStep2
     */
    public function setMemberInfo($member_info)
    {
        $this->_logic_buy_1->member_info = $member_info;
    }

    /**
     * 购买第一步
     * @param unknown $cart_id
     * @param unknown $ifcart
     * @param string $member_id
     * @param unknown $store_id
     * @param array $jjg
     * @param bool $ifchain
     * @return Ambigous <multitype:unknown, multitype:unknown >
     */
    public function buyStep1($cart_id, $ifcart, $member_id, $store_id, $jjg = null, $ifchain = null, $chain_id = null, $area_id = 0, $power_id = 0)
    {


        //得到购买商品信息
        if ($ifcart) {
            $result = $this->getCartList($cart_id, $member_id, $jjg, $chain_id, $area_id, 0);
        } else {
            $result = $this->getGoodsList($cart_id, $member_id, $store_id, $area_id, 0);
        }
        if (!$result['state']) {
            return $result;
        }

        //得到页面所需数据：收货地址、发票、代金券、预存款、商品列表等信息
        $result = $this->getBuyStep1Data($member_id, $result['data'], $area_id);

        $this->buyRecalculateSubGoodsPrice($result['data']['store_cart_list']);
        $result['data']['goods_list'] = current($result['data']['store_cart_list']);

        return $result;
    }

    /**
     * 第一步：处理购物车
     *
     * @param array $cart_id 购物车
     * @param int $member_id 会员编号
     */
    public function getCartList($cart_id, $member_id, $jjg = null, $chain_id = null, $area_id = 0, $is_decr_virtual_stock = 0)
    {
        $model_cart = Model('cart');
        $model_member = Model('member');
        $member_info = $model_member->getMemberInfoByID($member_id);
        //取得POST ID和购买数量
        $buy_items = $this->_parseItems($cart_id);
        if (empty($buy_items)) {
            return callback(false, '所购商品无效');
        }


        if (count($buy_items) > 50) {
            return callback(false, '一次最多只可购买50种商品');
        }

        //购物车列表
        $condition = array('cart_id' => array('in', array_keys($buy_items)), 'buyer_id' => $member_id);
        $cart_list  = $model_cart->listCart('db', $condition);

        //限购
        foreach ($cart_list as $k => $v) {
            if ($v['goods_num'] <= 0) {
                return callback(false, "商品 {$v['goods_name']} 数量无效");
            }

            //判断商品是否可以购买
            $logic_buy_1 = Logic('buy_1');
            $buy_goods = $logic_buy_1->memberGoodsCheck($v['goods_id'], $member_info);
            if ($buy_goods['state'] == false) {
                $data = [
                    'error' => $v['goods_name'] . $buy_goods['msg'],
                    'goods_id' => $v['goods_id'],
                    'needVip' => $buy_goods['data']['needVip'],
                    'needBzk' => $buy_goods['data']['needBzk'],
                ];
                output_data($data, [], true, 401);
            }

            if ($v['goods_limit'] > 0) {
                $edesc = '';
                if ($v['is_book'] == 1 || $v['is_presell'] == 1) {
                    $edesc = '预售';
                }

                $yimai = $this->_logic_buy_1->getmemberbuygoodssum($member_id, $v["goods_id"]);
                if ($yimai >= $v['goods_limit']) {
                    return callback(false, $edesc . '限购' . $v['goods_limit'] . '件,您已购买' . $yimai . '个');
                }
                $shengxia = $v['goods_limit'] - $yimai;
                if ($v['goods_num'] > $shengxia) {
                    if ($yimai > 0) {
                        $desc = $edesc . '限购' . $v['goods_limit'] . '件,您已购买' . $yimai . '个,还可以购买' . $shengxia . '个';
                    } else {
                        $desc = $edesc . '限购' . $v['goods_limit'] . '件';
                    }
                    return callback(false, $desc);
                }
            }
        }

        //购物车列表 [得到最新商品属性及促销信息]
        $cart_list = $this->_logic_buy_1->getGoodsCartList($cart_list, null, $area_id);

        // 计算加价购各个活动总金额
        $invalidCount = 0;
        foreach ($cart_list as $cart) {
            if (intval($_REQUEST['source']) == 1 && $cart['promotion_info']['promotion_type'] > 2 && $_POST['pay_name'] != '' || $cart['invalid_type'] > 0) { //竖屏购物车非限时活动时，返回失效
                $invalidCount++;
            }

            if ($cart['promotion_info']['promotion_type'] == 3) {
                $_n = Model('order')->getSpikeOrderLimit($member_id, $cart);
                if ($_n > 0) {
                    return callback(false, '抱歉，秒杀商品只能购买' . $_n . '次，请重新选择');
                }
            }
        }

        //商品列表 [优惠套装子商品与普通商品同级罗列]
        $goods_list = $this->_getGoodsList($cart_list);

        //以店铺下标归类
        $store_cart_list = $this->_getStoreCartList($cart_list);
        if (empty($store_cart_list) || !is_array($store_cart_list)) {
            return callback(false, '提交数据错误');
        }
        if (intval($_REQUEST['source']) == 1 && $_POST['pay_name'] != '' && $invalidCount > 0) { //竖屏购物车非限时活动时，返回失效
            throw (new SubmitStockException('商品已失效'))->setStockData(['is_stock' => true, 'datas' => $store_cart_list]);
        }

        return callback(true, '', array(
            'goods_list' => $goods_list,
            'store_cart_list' => $store_cart_list,
            'jjgValidSkus' => [],
            'jjgStoreCosts' => [],
        ));
    }

    protected function jjgPostDataParser($jjg)
    {
        $r = array();

        foreach ((array) $jjg as $j) {
            if (preg_match('/^(\d+)\|(\d+)\|(\d+)$/', $j, $m)) {
                $r[$m[1]][$m[2]][$m[3]] = 1;
            }
        }

        return $r;
    }

    /**
     * 第一步：处理立即购买
     *
     * @param array $cart_id 购物车
     * @param int $member_id 会员编号
     * @param int $store_id 店铺编号
     * @param int $is_decr_virtual_stock 是否扣减虚拟库存，只在buystep2扣减
     */
    public function getGoodsList($cart_id, $member_id, $store_id, $area_id = 0, $is_decr_virtual_stock = 0)
    {
        $model_member = Model('member');
        $member_info = $model_member->getMemberInfoByID($member_id);
        $model_goods = Model("goods");
        //取得POST ID和购买数量
        $buy_items = $this->_parseItems($cart_id);
        if (empty($buy_items)) {
            return callback(false, '所购商品无效');
        }
        //健康会员卡实体卡 vip-3.1
        $isPhysicalCardArr = $model_goods->isPhysicalCard(array_keys($buy_items));
        $isPhysicalCard = 0;    //是否是健康会员卡实体卡
        foreach ($isPhysicalCardArr as $k => $v) {
            if ($v > 0) {
                $isPhysicalCard = $v;
            }
        }
        if ($isPhysicalCard > 0) {
            if (count($isPhysicalCardArr) != 1) {
                return callback(false, '健康卡会员卡只能单独购买');
            }
        }
        //互联网医院 且 不是 健康会员卡实体卡
        if ($this->isHospital() && $isPhysicalCard == 0) {
            $goods_list = $store_cart_list = array();
            foreach ($buy_items as $goods_id => $quantity) {
                $goods_info = $this->_logic_buy_1->getGoodsOnlineInfo($goods_id, intval($quantity), $area_id);
                // 替换为互联网医疗的店铺名称
                if ($this->isExsitHospitalConsultOrderSn()) {
                    $goods_info['store_name'] = '金华物择贸易有限公司';
                }
                $goods_list[] = $goods_info;
                $store_cart_list[$goods_info['store_id']][] = $goods_info;
            }
            return callback(true, '', array('goods_list' => $goods_list, 'store_cart_list' => $store_cart_list, 'is_book' => 0));
        }

        $goods_id = key($buy_items);
        $quantity = current($buy_items);

        //商品信息[得到最新商品属性及促销信息]
        $goods_info = $this->_logic_buy_1->getGoodsOnlineInfo($goods_id, intval($quantity), $area_id);
        if (empty($goods_info)) {
            return callback(false, '商品已下架或不存在');
        }

        //判断商品是否可以购买
        /** @var buy_1Logic $logic_buy_1*/
        $logic_buy_1 = Logic('buy_1');
        $buy_goods = $logic_buy_1->memberGoodsCheck($goods_info['goods_id'], $member_info);
        if ($buy_goods['state'] == false) {
            $data = [
                'error' => $buy_goods['msg'],
                'needVip' => $buy_goods['data']['needVip'],
                'needBzk' => $buy_goods['data']['needBzk'],
            ];
            output_data($data, [], true, 401);
        }

        $this->bookBuyInfo = $bookBuy = BookBuyProduct::getActiveInfo($goods_info['goods_id']);

        // 如果不是预售
        if ($_POST['activity_type'] <> 1 && $bookBuy) {
            return callback(false, '商品已开始预售，价格有变化', [
                'action_text' => '查看商品',
                'action' => 2, // 1刷新、2查看商品、3返回订单详情、4取消订单、5上一页
            ]);
        }

        // 新人专享活动
        if ($_POST['activity_type'] == 2 && $isPhysicalCard == 0) {
            $this->newcomerInfo = NewBuyProduct::getActiveInfo($goods_info['goods_id']);
            if (empty($this->newcomerInfo)) {
                return callback(false, '新人活动结束，价格有变化', [
                    'action_text' => '查看商品',
                    'action' => 2, // 1刷新、2查看商品、3返回订单详情、4取消订单、5上一页
                ]);
            }
            if (!MemberAlias::isNewcomer($member_info)) {
                return callback(false, '抱歉，您已不符合新人身份', [
                    'action_text' => '查看商品',
                    'action' => 2, // 1刷新、2查看商品、3返回订单详情、4取消订单、5上一页
                ]);
            }
            $goods_info['activity_type'] = 2;
            $goods_info['goods_price'] = ncPriceFormat($this->newcomerInfo['new_price'] / 100);
        } elseif ($_POST['activity_type'] == 1 && $isPhysicalCard == 0) { // 定金预售
            if (empty($bookBuy)) {
                return callback(false, '抱歉，商品预售活动时间已结束，无法结算。', [
                    'action_text' => '查看商品',
                    'action' => 2, // 1刷新、2查看商品、3返回订单详情、4取消订单、5上一页
                ]);
            }
            $bookModel = new BookBuyProduct();
            $goods_info['activity_type'] = 1;
            $goods_info['pre_info'] = $bookModel->formatPreInfo($bookBuy, $quantity);
            $goods_info['goods_price'] = ncPriceFormat($bookBuy['book_price'] / 100);
        } elseif ($_POST['activity_type'] == 3 && $isPhysicalCard == 0) { //新秒杀
            $skProduct = PromotionProduct::getSkProductInfo($goods_info['goods_id']);
            if (empty($skProduct)) {
                return callback(false, '抱歉，商品秒杀活动时间已结束，无法结算。', [
                    'action_text' => '查看商品',
                    'action' => 2, // 1刷新、2查看商品、3返回订单详情、4取消订单、5上一页
                ]);
            }

            $goods_info['activity_type'] = 3;
            // 描述是否免邮
            $goods_info['sk_is_shipping_free'] = $skProduct['is_shipping_free'];
            $goods_info['goods_price'] = ncPriceFormat($skProduct['seckill_price'] / 100);
        } elseif ($_POST['activity_type'] == 19 && $isPhysicalCard == 0) { // 会员0元购
            try {
                $cardGift = VipCardGift::check($member_info, $goods_id, $_POST['consult_order_sn']);
                $goods_info['activity_type'] = 19;
                $goods_info['goods_price'] = ncPriceFormat($cardGift['price']);
            } catch (\Exception $e) {
                return callback(false, $e->getMessage(), [
                    'action_text' => '返回',
                    'action' => 5, // 1刷新、2查看商品、3返回订单详情、4取消订单、5上一页
                ]);
            }
        } elseif ($_POST['pintuan'] > 0 && $isPhysicalCard == 0) { //判断拼团限购
            //0=拼团  1：参团
            $pin_type = intval($_POST['pin_type']);
            $gid = intval($_POST['gid']);
            if ($pin_type == 1) {
                $pin_group_info = DcGroupBuyProduct::getJoinGroupProductInfo($gid, $goods_id);
            } else {
                $pin_group_info = DcGroupBuyProduct::getGroupProductInfo($gid, $goods_id);
            }
            if (is_array($pin_group_info) && !empty($pin_group_info)) {
                if ($quantity > $pin_group_info['buy_limit_num']) {
                    return callback(false, '限购' . $pin_group_info['buy_limit_num']);
                }
                $goods_info['pintuan_promotion'] = '1';
                $goods_info['pintuan_info'] = $pin_group_info;
                $goods_info['old_goods_price'] = $goods_info['goods_price'];
                $goods_info['goods_price'] = ncPriceFormat($pin_group_info['price'] / 100);
            } else {
                return callback(false, '活动已过期或终止');
            }
        } elseif ($_POST['cycle_num'] > 1 && $isPhysicalCard == 0) { //周期购
            $cycle_info = DcCycleBuyProduct::getCycleProductInfo($goods_info['goods_id'], $_POST['cycle_num']);
            if (!empty($cycle_info)) {
                $goods_info['old_goods_price'] = $goods_info['goods_price'];
                $goods_info['goods_price'] = $goods_info['goods_price'];
                if ($cycle_info['three_price'] > 0) {
                    $goods_info['three_price'] = $cycle_info['three_price'];
                }
                if ($cycle_info['six_price'] > 0) {
                    $goods_info['six_price'] = $cycle_info['six_price'];
                }
                if ($cycle_info['twelve_price'] > 0) {
                    $goods_info['twelve_price'] = $cycle_info['twelve_price'];
                }
                //获取每期价格
                $goods_info['cycle_num'] = $_POST['cycle_num'];
                $goods_info['cycle_price'] = $cycle_info['cycle_price'];
            } else {
                return callback(false, '活动已结束或终止');
            }
        } else {
            if ($member_id && $goods_info['goods_limit'] > 0) {
                $edesc = '';
                if ($goods_info['is_book'] == 1 || $goods_info['is_presell'] == 1) {
                    $edesc = '预售';
                }
                //已购数量
                $yimai = $this->_logic_buy_1->getmemberbuygoodssum($member_id, $goods_info["goods_id"]);

                if ($yimai >= $goods_info['goods_limit']) {
                    return callback(false, $edesc . '限购' . $goods_info['goods_limit'] . '件,您已购买' . $yimai . '个');
                }
                //剩下可买数
                $shengxia = $goods_info['goods_limit'] - $yimai;

                if ($quantity > $shengxia) {
                    if ($yimai > 0) {
                        $desc = $edesc . '限购' . $goods_info['goods_limit'] . '件,您已购买' . $yimai . '个,还可以购买' . $shengxia . '个';
                    } else {
                        $desc = $edesc . '限购' . $goods_info['goods_limit'] . '件';
                    }
                    return callback(false, $desc);
                }
            }

            if (intval($_POST['ifchain']) == 1 && intval($_POST['chain_id']) > 0) {
                $chain_id = intval($_POST['chain_id']);
                $stock_info = Model('chain_stock')->getChainStockInfo(array('goods_id' => $goods_id, 'stock' => array('gt', 0), 'chain_id' => $chain_id), 'chain_id,chain_price');
                if (!empty($stock_info) && $stock_info['chain_price'] > 0) { //门店验证
                    $goods_info['pintuan_promotion'] = 0;
                    $goods_info['goods_price'] = $stock_info['chain_price'];
                    $goods_info['chain_id'] = $stock_info['chain_id'];
                    $goods_info['is_chain'] = 1;
                } else {
                    $goods_info['is_chain'] = 0;
                }
            }

            $power_id = intval($_POST['power_id']);
            //如果是助力活动返回助力价格lihaobin
            if ($power_id && !$_POST['ifchain']) {
                $power_info = Model('power')->getPowerInfo(['power_id' => $power_id, 'power_goods_id' => $goods_info['goods_id']]);
                if (!empty($power_info) && $power_info['power_status'] == 1) {
                    $goods_info['goods_price'] = $power_info['power_price'];
                }
                //结束加1分钟
                if (!empty($power_info) && ($power_info['power_status'] == 3) && ($power_info['power_etime'] + 60) < TIMESTAMP && intval($_POST['power_state']) != 1) {
                    return callback(false, '助力补贴活动已结束');
                }
                $goods_info['goods_num'] = 1;
            }
        }
        //进一步处理数组
        $store_cart_list = array();
        $goods_list = array();
        $goods_list[0] = $store_cart_list[$goods_info['store_id']][0] = $goods_info;

        return callback(true, '', array('goods_list' => $goods_list, 'store_cart_list' => $store_cart_list, 'is_book' => $goods_info['is_book']));
    }

    /**
     * 购买第一步：返回商品、促销、地址、发票等信息，然后交前台抛出
     *
     * @param string $member_id
     * @param unknown $data 商品信息
     * @return
     */
    public function getBuyStep1Data($member_id, $data, $area_id)
    {
        $store_id = $_SESSION['store_id'] ?: $_REQUEST['store_id'] ?: 1;

        $goods_list = $data['goods_list'];
        $store_cart_list = $data['store_cart_list'];

        //商品金额计算(分别对每个商品/优惠套装小计、每个店铺小计)//增加member_id 作为限时优惠商品数量判断
        list($store_cart_list, $store_goods_total, $goods) = $this->_logic_buy_1->calcCartList($store_cart_list, $member_id);

        //定义返回数组
        $result = array();

        $result['store_cart_list'] = $store_cart_list;
        $result['store_goods_total'] = $store_goods_total;

        if (APP_ID == 'mobile') {
            $result['store_goods_total_1'] = $store_goods_total;
        }
        $model_goods = Model("goods");
        $isPhysicalCard = $model_goods->isPhysicalCard([$store_cart_list[$store_id][0]["goods_id"]])[$store_cart_list[$store_id][0]["goods_id"]];
        //预定商品不使用任何优惠
        if (!$data['is_book'] && $_POST['activity_type'] != 19 && $isPhysicalCard == 0) {
            //取得店铺优惠 - 满即送(赠品列表，店铺满送规则列表)
            list($store_premiums_list, $store_mansong_rule_list) = $this->_logic_buy_1->getMansongRuleCartListByTotal($store_goods_total);

            $result['store_premiums_list'] = $store_premiums_list;
            $result['store_mansong_rule_list'] = $store_mansong_rule_list;

            //重新计算优惠后(满即送)的店铺实际商品总金额
            $store_goods_total = $this->_logic_buy_1->reCalcGoodsTotal($store_goods_total, $store_mansong_rule_list, 'mansong');

            if (APP_ID == 'mobile') {
                $result['store_goods_total_1'] = $store_goods_total;
            }
            if (intval($_POST['ifchain']) == 1 && intval($_POST['chain_id']) > 0) { //返回门店可用的代金券
                $result['store_voucher_list'] = $this->_logic_buy_1->getChainAvailableVoucherList($store_goods_total, $member_id, intval($_POST['chain_id']));
            } else { //返回店铺可用的代金券
                $result['store_voucher_list'] = $this->_logic_buy_1->getStoreAvailableVoucherList($store_goods_total, $member_id, $goods);
            }
            //如果是助力，则去掉优惠卷
            if (intval($_POST['power_id'])) {
                $result['store_voucher_list'] = '';
            }
        } else {
            $result['store_premiums_list'] = $result['store_mansong_rule_list'] = $result['store_voucher_list'] = $result['rpt_list'] = array();
        }

        //如果收货地址属于偏远地区，运费从运费模板取
        $transport_id = $this->getRemoteAreasTransportId($area_id);
        if ($isPhysicalCard > 0) {
            $transport_id = 0;
        }
        //输出符合满X元包邮条件的店铺ID及包邮设置信息
        if ($transport_id <= 0 && (($isPhysicalCard > 0) || ($this->newcomerInfo && $this->newcomerInfo['is_shipping_free']) ||
            ($this->bookBuyInfo && $this->bookBuyInfo['is_shipping_free']) ||
            ($goods_list[0]['activity_type'] == 3) && $goods_list[0]['sk_is_shipping_free'])) {
            $cancel_calc_sid_list = [];
            $freight_list = [];
        } else {
            //将商品的运费设置从运费模板取
            if ($transport_id > 0) {
                $cancel_calc_sid_list = [];
                foreach ($goods_list as $k => $v) {
                    $goods_list[$k]["transport_id"] = $transport_id;
                    $goods_list[$k]['freight'] = 1;
                    $goods_list[$k]['goods_freight'] = 0;
                }
            } else {
                $cancel_calc_sid_list = $this->_logic_buy_1->getStoreFreightDescList($store_goods_total);
            }

            //将商品ID、运费模板、运费序列化，加密，输出到模板，选择地区AJAX计算运费时作为参数使用
            $freight_list = $this->_logic_buy_1->getStoreFreightList($goods_list, array_keys($cancel_calc_sid_list));
        }

        $result['cancel_calc_sid_list'] = $cancel_calc_sid_list;
        $result['freight_list'] = $this->buyEncrypt($freight_list, $member_id);

        //输出用户默认收货地址
        $result['address_info'] = Model('address')->getDefaultAddressInfo(array('member_id' => $member_id, 'store_id' => $store_id));

        return callback(true, '', $result);
    }

    /**
     * 比较是否属于偏远地区，如果是返回运费模板id
     * @param int $area_id
     * @return int transport_id
     */
    public function getRemoteAreasTransportId($area_id)
    {
        if ($_REQUEST['store_id'] == 3) {
            $setting_info = Model("setting")->getRowSetting("remote_areas_transport_csy");
        } else {
            $setting_info = Model("setting")->getRowSetting("remote_areas_transport_id");
        }

        if (!$setting_info["value"]) {
            return 0;
        }

        $where["transport_id"] = $setting_info["value"];
        $where["area_id"] = array('like', '%,' . $area_id . ',%');
        $transport_info = Model("transport")->getExtendInfo($where);

        if (!$transport_info) {
            return 0;
        }

        return $setting_info["value"];
    }

    /**
     * 购买第二步
     * @param array $post
     * @param int $member_id
     * @param string $member_name
     * @param string $member_email
     * @return array
     */
    public function buyStep2($post, $member)
    {

        $this->_member_info = $member;
        $member_id = $member['member_id'];
        $this->_post_data = $post;

        $lock = Redis::lock('order:buyStep2:' . $member_id, 10)->setAutoRelease();
        if (!$lock->get()) {
            throw new Exception('订单处理中，请不要重复提交');
        }

        try {
            $model = Model('order');
            $model->beginTransaction();
            //第1步 表单验证
            $this->_createOrderStep1();

            //第2步 得到购买商品信息
            $this->_createOrderStep2();

            //第3步 得到购买相关金额计算等信息
            $this->_createOrderStep3();

            //第4步 生成订单
            $this->_createOrderStep4();

            //第6步 订单后续处理
            $this->_createOrderStep6();

            $model->commit();

            $this->syncVirtualStock(true);

            return callback(true, '', $this->_order_data);
        } catch (Exception $e) {
            $model->rollback();

            $this->syncVirtualStock();

            if ($e instanceof SubmitStockException) {
                output_data($e->getStockData());
            }

            return callback(false, $e->getMessage());
        }
    }

    /**
     * 购买第二步 提交拼团订单到数据中心
     * @param array $post
     * @param int $member_id
     * @param string $member_name
     * @param string $member_email
     * @return array
     */
    public function buyStep3($post, $member_id, $member_name, $member_email, $member_mobile)
    {
        $this->_member_info['member_id'] = $member_id;
        $this->_member_info['member_name'] = $member_name;
        $this->_member_info['member_email'] = $member_email;
        $this->_member_info['member_level'] = $member_level = 0;
        $this->_member_info['member_mobile'] = $member_mobile;
        $this->_post_data = $post;
        try {
            //第1步 表单验证
            $this->_createOrderStep1();

            //第2步 得到购买商品信息
            $this->_createOrderStep2();

            //第3步 得到购买相关金额计算等信息
            $this->_createOrderStep3();

            //第4步 生成订单
            $this->_createOrderStep11();
            return callback(true, '', $this->_order_data);
        } catch (Exception $e) {
            return callback(false, $e->getMessage());
        }
    }

    /**
     * 互联网医疗订单下单逻辑
     * @param $post
     * @param $memberInfo
     * @return array
     */
    public function buyStepForHospital($post, $memberInfo)
    {
        $this->_member_info = $memberInfo;
        $this->_member_info['member_level'] = 0;
        $this->_post_data = $post;
        try {
            $model = Model('order');
            $model->beginTransaction();
            //第1步 表单验证
            $this->_createOrderStep1();

            //第2步 得到购买商品信息
            $this->_createOrderStep2();

            //第3步 得到购买相关金额计算等信息
            $this->_createOrderStep3();

            //第4步 生成订单
            $this->_createOrderForHospital();

            $model->commit();

            return callback(true, '', $this->_order_data);
        } catch (Exception $e) {

            $model->rollback();

            return callback(false, $e->getMessage());
        }
    }

    /**
     * 删除购物车商品
     * @param unknown $ifcart
     * @param unknown $cart_ids
     */
    public function delCart($ifcart, $member_id, $cart_ids)
    {
        if (!$ifcart || !is_array($cart_ids)) return;
        $cart_id_str = implode(',', $cart_ids);
        if (preg_match('/^[\d,]+$/', $cart_id_str)) {
            Logic('queue')->delCart(array('buyer_id' => $member_id, 'cart_ids' => $cart_ids));
        }
    }

    /**
     * 根据门店自提站ID计算商品库存，返回库存不足的商品ID
     * @param int $chain_id
     * @param string $product
     * @return array|null
     */
    public function changeChain($chain_id = 0, $product = '')
    {
        $chain_id = intval($chain_id);
        if ($chain_id <= 0) return null;
        if (strpos($product, '-') !== false) {
            $product = explode('-', $product);
        } else {
            $product = array($product);
        }
        if (empty($product) || !is_array($product)) return null;
        $product = $this->_parseItems($product);
        $condition = array();
        $condition['goods_id'] = array('in', array_keys($product));
        $condition['chain_id'] = $chain_id;
        $list = Model('chain_stock')->getChainStockList($condition);
        if ($list) {
            $_tmp = array();
            foreach ($list as $v) {
                $_tmp[$v['goods_id']] = $v['stock'];
            }
            foreach ($product as $goods_id => $num) {
                if ($_tmp[$goods_id] >= $num) {
                    unset($product[$goods_id]);
                }
            }
        }
        $data = array();
        $data['state'] = 'success';
        $data['product'] = array_keys($product);
        return $data;
    }

    /**
     * 选择不同地区时，异步处理并返回每个店铺总运费以及本地区是否能使用货到付款
     * 如果店铺统一设置了满免运费规则，则运费模板无效
     * 如果店铺未设置满免规则，且使用运费模板，按运费模板计算，如果其中有商品使用相同的运费模板,作为一种商品算运费
     * 如果未找到运费模板，按免运费处理
     * 如果没有使用运费模板，商品运费按快递价格计算，运费不随购买数量增加
     */
    public function changeAddr($freight_hash, $city_id, $area_id, $member_id)
    {
        //$city_id计算运费模板,$area_id计算货到付款
        $city_id = intval($city_id);
        $area_id = intval($area_id);
        if ($city_id <= 0 || $area_id <= 0) return null;

        //将hash解密，得到运费信息(店铺ID，运费,运费模板ID),hash内容有效期为1小时
        $freight_list = $this->buyDecrypt($freight_hash, $member_id);
        //算运费
        list($store_freight_list, $no_send_tpl_ids) = $this->_logic_buy_1->calcStoreFreight($freight_list, $city_id);
        $data = array();
        $data['state'] = empty($store_freight_list) && empty($no_send_tpl_ids) ? 'fail' : 'success';
        $data['content'] = $store_freight_list;
        $data['no_send_tpl_ids'] = $no_send_tpl_ids;

        $offline_store_id_array = Model('store')->getOwnShopIds();
        $offline_pay = Model('payment')->getPaymentOpenInfo(array('payment_code' => 'offline'));

        if ($offline_store_id_array && $offline_pay) {
            $info = Model('area')->getAreaInfo(array('area_id' => $area_id));
            if ($info['area_deep'] == 4) $area_id = $info['area_parent_id'];
            $allow_offpay_batch = Model('offpay_area')->checkSupportOffpayBatch($area_id, array_values($offline_store_id_array));

            //JS验证使用
            $data['allow_offpay'] = array_filter($allow_offpay_batch) ? '1' : '0';
            $data['allow_offpay_batch'] = $allow_offpay_batch;
        } else {
            //JS验证使用
            $data['allow_offpay'] = '0';
            $data['allow_offpay_batch'] = array();
        }

        //PHP验证使用
        $data['offpay_hash'] = $this->buyEncrypt($data['allow_offpay'] ? 'allow_offpay' : 'deny_offpay', $member_id);
        $data['offpay_hash_batch'] = $this->buyEncrypt($data['allow_offpay_batch'], $member_id);
        return $data;
    }

    /**
     * 验证F码
     * @param int $goods_commonid
     * @param string $fcode
     * @return array
     */
    public function checkFcode($goods_id, $fcode)
    {
        $fcode_info = Model('goods_fcode')->getGoodsFCode(array('goods_id' => $goods_id, 'fc_code' => $fcode, 'fc_state' => 0));
        if ($fcode_info) {
            return callback(true, '', $fcode_info);
        } else {
            return callback(false, 'F码错误');
        }
    }

    /**
     * 订单生成前的表单验证与处理
     *
     */
    private function _createOrderStep1()
    {

        $post = $this->_post_data;
        //取得商品ID和购买数量
        $input_buy_items = $this->_parseItems($post['cart_id']);
        if (empty($input_buy_items)) {
            throw new Exception('所购商品无效');
        }
        //验证收货地址
        if (empty($input_address_info = $post['input_address_info'])) {
            $input_address_info = Model('address')->getAddressInfo([
                'address_id' => $post['address_id'],
                'member_id' => $this->_member_info['member_id']
            ]);
            if (empty($input_address_info)) {
                throw new Exception('请选择收货地址');
            }
        }
        if ($input_address_info['dlyp_id']) {
            $input_dlyp_id = $input_address_info['dlyp_id'];
        }
        //收货地址城市编号
        $input_city_id = intval($input_address_info['city_id']);
        $input_if_offpay = false;
        $input_if_offpay_batch = array();
        //付款方式:在线支付/货到付款(online/offline)
        if (!in_array($post['pay_name'], array('online', 'offline', 'chain'))) {
            throw new Exception('付款方式错误，请重新选择');
        }
        $input_pay_name = $post['pay_name'];

        //验证代金券
        $input_voucher_list = array();
        if (!empty($post['voucher']) && is_array($post['voucher'])) {
            foreach ($post['voucher'] as $store_id => $voucher) {
                $_info = array();
                if ($post['pintuan'] == 2) {
                    $_info['voucher_price'] = $voucher['voucher_price'];
                    $input_voucher_list[$store_id]['voucher_price'] = $_info['voucher_price'];
                } else {
                    if (preg_match_all('/^(\d+)\|(\d+)\|([\d.]+)$/', $voucher, $matchs)) {
                        if (floatval($matchs[3][0]) > 0) {
                            $input_voucher_list[$store_id]['voucher_t_id'] = $matchs[1][0];
                            $input_voucher_list[$store_id]['voucher_price'] = 0;
                            // upet_chain_voucher代金券没有用
                            if (!$post['chain']['id']) {
                                $_info = Model('voucher')->table('voucher')->where(array('voucher_store_id' => $store_id, 'voucher_t_id' => $matchs[1][0], 'voucher_owner_id' => $this->_member_info['member_id'], 'voucher_state' => 1))->master(true)->lock(true)->find(); //锁定当前代金券记录
                            } else {
                                $_info = Model('chain_voucher')->table('chain_voucher')->where(array('voucher_t_id' => $matchs[1][0], 'voucher_owner_id' => $this->_member_info['member_id'], 'voucher_state' => 1))->master(true)->lock(true)->find(); //锁定当前代金券记录
                            }
                            if ($_info['voucher_state'] != 1) {
                                throw new Exception('请选择正确的代金券');
                            }
                            $input_voucher_list[$store_id]['voucher_price'] = $_info['voucher_price'];
                        }
                    }
                }
            }
        }
        //保存数据
        $this->_order_data['input_buy_items'] = $input_buy_items;
        $this->_order_data['input_city_id'] = $input_city_id;
        $this->_order_data['input_pay_name'] = $input_pay_name;
        $this->_order_data['input_if_offpay'] = $input_if_offpay;
        $this->_order_data['input_if_offpay_batch'] = $input_if_offpay_batch;
        $this->_order_data['input_pay_message'] = $post['pay_message'];
        $this->_order_data['input_address_info'] = $input_address_info;
        $this->_order_data['input_dlyp_id'] = 0;
        $this->_order_data['input_chain_id'] = 0;
        $this->_order_data['input_invoice_info'] = [];
        $this->_order_data['input_voucher_list'] = $input_voucher_list;
        $this->_order_data['input_rpt_info'] = [];
        $this->_order_data['order_from'] = $post['order_from'] ?: 1;
        $this->_order_data['input_is_book'] = $post['is_book'];
    }

    /**
     * 得到购买商品信息
     *
     */
    private function _createOrderStep2()
    {

        $post = $this->_post_data;
        $input_buy_items = $this->_order_data['input_buy_items'];
        $input_is_book = $this->_order_data['input_is_book'];
        $input_address_info = Model('address')->getAddressInfo(array('address_id' => intval($post['address_id'])));

        if ($this->isHospital()) { // 互联网医疗非购物车通道支持多商品下单
            $store_cart_list = array();
            foreach ($post['cart_id'] as $cart) {
                $arrCart = explode('|', $cart);
                $goods_info = $this->_logic_buy_1->getGoodsOnlineInfo($arrCart[0], intval($arrCart[1]), $input_address_info['city_id']);
                if (!$this->isExsitHospitalConsultOrderSn() && isset($goods_info['goods_state']) && $goods_info['goods_state'] != 1) { // 推荐id的下单，需要判断上下架状态
                    throw new Exception('商品已下架或不存在');
                }
                $store_cart_list[$goods_info['store_id']][] = $goods_info;
            }
        } else {
            if ($post['ifcart']) {
                //购物车列表
                $model_cart = Model('cart');
                $condition = array(
                    'cart_id' => array('in', array_keys($input_buy_items)),
                    'buyer_id' => $this->_member_info['member_id'],
                    'store_id' => $post['store_id'],
                );
                $cart_list  = $model_cart->listCart('db', $condition);

                // 加价购条件
                $jjgObj = null;
                //购物车列表 [得到最新商品属性及促销信息]
                $cart_list = $this->_logic_buy_1->getGoodsCartList($cart_list, $jjgObj, $input_address_info['city_id']);

                //以店铺下标归类
                $store_cart_list = $this->_getStoreCartList($cart_list);
            } else {
                //来源于直接购买
                $goods_id = key($input_buy_items);
                $quantity = current($input_buy_items);

                //商品信息[得到最新商品属性及促销信息]
                $goods_info = $this->_logic_buy_1->getGoodsOnlineInfo($goods_id, intval($quantity), $input_address_info['city_id']);
                if (empty($goods_info)) {
                    throw new Exception('商品已下架或不存在');
                }

                //预定不享受任何优惠
                if ($input_is_book && $goods_info['is_book']) {
                    $input_is_book = true;
                } else {
                    $input_is_book = false;
                }
                $this->_order_data['input_is_book'] = $input_is_book;

                $bookBuy = BookBuyProduct::getActiveInfo($goods_info['goods_id']);

                // 如果不是预售
                if ($_POST['activity_type'] <> 1 && $bookBuy) {
                    throw new Exception('商品已开始预售，价格有变化，请刷新页面');
                }

                // 新人专享活动
                if ($_POST['activity_type'] == 2) {
                    $this->newcomerInfo = NewBuyProduct::getActiveInfo($goods_info['goods_id']);
                    if (empty($this->newcomerInfo)) {
                        throw new Exception('新人活动结束，价格有变化');
                    }
                    if (!MemberAlias::isNewcomer($this->_member_info)) {
                        throw new Exception('抱歉，您已不符合新人身份，无法结算');
                    }
                    $goods_info['activity_type'] = 2;
                    $goods_info['goods_price'] = ncPriceFormat($this->newcomerInfo['new_price'] / 100);
                } elseif ($_POST['activity_type'] == 1) { // 定金预售
                    if (empty($bookBuy)) {
                        throw new Exception('抱歉，商品预售活动时间已结束，无法结算');
                    }
                    $bookModel = new BookBuyProduct();
                    $goods_info['activity_type'] = 1;
                    $goods_info['pre_info'] = $bookModel->formatPreInfo($bookBuy, $quantity);
                    $goods_info['goods_price'] = ncPriceFormat($bookBuy['book_price'] / 100);
                } elseif ($_POST['activity_type'] == 19) { // 会员0元购
                    try {
                        $cardGift = VipCardGift::check($this->_member_info, $goods_id, $_POST['consult_order_sn']);
                        $goods_info['activity_type'] = 19;
                        $goods_info['card_gift'] = $cardGift;
                        $goods_info['goods_price'] = ncPriceFormat($cardGift['price']);
                    } catch (\Exception $e) {
                        return callback(false, $e->getMessage(), [
                            'action_text' => '返回',
                            'action' => 5, // 1刷新、2查看商品、3返回订单详情、4取消订单、5上一页
                        ]);
                    }
                } elseif ($post['pintuan'] > 0) {
                    if ($post['pintuan'] == 1) {
                        if ($post['pin_type'] == 1) {
                            $pin_group_info = DcGroupBuyProduct::getJoinGroupProductInfo($post['gid'], $goods_info['goods_id']);
                        } else {
                            $pin_group_info = DcGroupBuyProduct::getGroupProductInfo($post['gid'], $goods_info['goods_id']);
                        }
                        if (is_array($pin_group_info) && !empty($pin_group_info)) {
                            $goods_info['pintuan_info'] = $pin_group_info;
                            $goods_info['pintuan_promotion'] = '1'; //增加
                            $goods_info['promotions_id'] = $pin_group_info['gid'];
                            $goods_info['goods_old_price'] = $goods_info['goods_price'];
                            $goods_info['goods_price'] = $pin_group_info['price'];
                        }
                    } else {
                        //接收拼团成功数据
                        $pin_group_info = $post['pin_info'];
                        $goods_info['pintuan_info'] = $pin_group_info;
                        $goods_info['pintuan_promotion'] = '1'; //增加
                        $goods_info['promotions_id'] = $post['gid'];
                        $goods_info['goods_old_price'] = $pin_group_info['goods_old_price'];
                        $goods_info['goods_price'] = $pin_group_info['goods_price'];
                    }
                } elseif (intval($this->_post_data['cycle_num']) > 1) {
                    // 周期购
                    $cycle_price = DcCycleBuyProduct::getCycleProductPrice($goods_id, intval($this->_post_data['cycle_num']));
                    if (!empty($cycle_price)) {
                        $goods_info['goods_price'] = $cycle_price;
                    } else {
                        throw new Exception('活动已结束或终止');
                    }
                }

                if (intval($goods_info['is_chain']) == 1 && intval($this->_order_data['input_chain_id']) > 0) {
                    $chain_id = intval($this->_order_data['input_chain_id']);
                    $stock_info = Model('chain_stock')->getChainStockInfo(array('goods_id' => $goods_id, 'stock' => array('gt', 0), 'chain_id' => $chain_id), 'chain_id,chain_price');
                    if (!empty($stock_info) && $stock_info['chain_price'] > 0) {
                        $goods_info['pintuan_promotion'] = 0;
                        $goods_info['goods_price'] = $stock_info['chain_price'];
                    }
                }
                $goods_info['is_live'] = $this->_post_data['is_live'];
                //进一步处理数组
                $store_cart_list = array();
                $goods_list = array();
                if ($post['power_id'] && !$post['ifcart']) {
                    $power_info = Model('power')->getPowerInfo(['power_id' => $post['power_id'], 'power_goods_id' => $goods_info['goods_id'], 'power_status' => 1]);
                    if ($power_info) {
                        $goods_info['goods_price'] = $power_info['power_price'];
                        $goods_info['goods_num'] = 1;
                        $goods_info['is_power'] = true;
                    }
                }
                $goods_list[0] = $store_cart_list[$goods_info['store_id']][0] = $goods_info;
            }
            $this->buyRecalculateSubGoodsPrice($store_cart_list);
        }

        $goods_list = $store_cart_list[$post['store_id']];

        //保存数据
        $this->_order_data['goods_list'] = $goods_list;
        $this->_order_data['store_cart_list'] = $store_cart_list;

        // 保存加价购数据
        $this->_order_data['jjgValidSkus'] = [];
        $this->_order_data['jjgStoreCosts'] = [];

        //验证门店自提
        if ($this->_order_data['input_chain_id'] && (count($store_cart_list) > 1 || !$this->_checkChain(current($store_cart_list)))) {
            $this->_order_data['input_chain_id'] = null;
        }
    }

    /**
     * 得到购买相关金额计算等信息
     *
     */
    private function _createOrderStep3()
    {
        $goods_list = $this->_order_data['goods_list'];
        $store_cart_list = $this->_order_data['store_cart_list'];
        $input_voucher_list = $this->_order_data['input_voucher_list'];
        $input_city_id = $this->_order_data['input_city_id'];
        $input_rpt_info = $this->_order_data['input_rpt_info'];
        $input_is_book = $this->_order_data['input_is_book'];
        $is_pintuan = $this->_post_data['pintuan'];
        //商品金额计算(分别对每个商品/优惠套装小计、每个店铺小计)
        list($store_cart_list, $store_goods_total) = $this->_logic_buy_1->calcCartList($store_cart_list, $this->_member_info['member_id']);
        //加价购 增加订单总额
        foreach ((array) $store_goods_total as $k => $v) {
            if (isset($this->_order_data['jjgStoreCosts'][$k])) {
                $v += $this->_order_data['jjgStoreCosts'][$k];
                $store_goods_total[$k] = ncPriceFormat($v);
            }
        }

        //如果收货地址属于偏远地区，运费从运费模板取
        $transport_id = $this->getRemoteAreasTransportId($this->_order_data["input_address_info"]["city_id"]);
        // 健康卡会员卡实体卡 vip-3.1
        $model_goods = Model("goods");
        $isPhysicalCard = $model_goods->isPhysicalCard([$goods_list[0]['goods_id']])[$goods_list[0]['goods_id']];

        //预定不享受任何优惠 健康会员卡实体卡 vip-3.1
        if (!$input_is_book && !$this->isHospital() && $isPhysicalCard == 0) {
            //周期购不参与满即送
            if (intval($this->_post_data['cycle_num']) > 1 || $_POST['activity_type'] == 19) {
                $store_final_goods_total = $store_goods_total;
            } else {
                //取得店铺优惠 - 满即送(赠品列表，店铺满送规则列表)
                list($store_premiums_list, $store_mansong_rule_list) = $this->_logic_buy_1->getMansongRuleCartListByTotal($store_goods_total);
                //重新计算店铺扣除满即送后商品实际支付金额
                $store_final_goods_total = $this->_logic_buy_1->reCalcGoodsTotal($store_goods_total, $store_mansong_rule_list, 'mansong');
            }
            // 计算每个店铺运费
            if ($this->_order_data['input_chain_id']) { //使用门店自提时免运费
                $store_freight_total[key($store_final_goods_total)] = 0;
                //得到有效的代金券
                $input_voucher_list = $this->_logic_buy_1->reChainParseVoucherList($input_voucher_list, $store_final_goods_total, $this->_member_info['member_id'], $this->_order_data['input_chain_id']);
            } else {
                if ($transport_id <= 0 && (($this->newcomerInfo && $this->newcomerInfo['is_shipping_free']) ||
                    ($this->bookBuyInfo && $this->bookBuyInfo['is_shipping_free']))) {
                    $store_freight_total[key($store_final_goods_total)] = 0;
                } else {
                    //将商品的运费设置从运费模板取
                    if ($transport_id > 0) {
                        $cancel_calc_sid_list = [];
                        foreach ($goods_list as $k => $v) {
                            $goods_list[$k]["transport_id"] = $transport_id;
                            $goods_list[$k]['freight'] = 1;
                            $goods_list[$k]['goods_freight'] = 0;
                        }
                    } else {
                        //取得包邮的店铺ID信息
                        $cancel_calc_sid_list = $this->_logic_buy_1->getStoreFreightDescList($store_final_goods_total);
                    }

                    $freight_list = $this->_logic_buy_1->getStoreFreightList($goods_list, array_keys($cancel_calc_sid_list));
                    list($store_freight_total, $no_send_tpl_ids) = $this->_logic_buy_1->calcStoreFreight($freight_list, $input_city_id);
                }
                //得到有效的代金券
                if ($is_pintuan == 2) {
                    $input_voucher_list  = $this->_post_data['voucher'];
                } else {
                    $input_voucher_list = $this->_logic_buy_1->reParseVoucherList($input_voucher_list, $store_final_goods_total, $this->_member_info['member_id']);
                }
            }

            if ($is_pintuan > 0) {
                $goods_id = $goods_list[0]['goods_id'];
                $pin_type = intval($this->_post_data['pin_type']);
                $gid = intval($this->_post_data['gid']);

                if ($transport_id <= 0) {
                    // 拼团 判断活动是否设置运费
                    if ($is_pintuan == 1) {
                        if ($pin_type == 1) {
                            $pin_group_info = DcGroupBuyProduct::getJoinGroupProductInfo($gid, $goods_id);
                        } else {
                            $pin_group_info = DcGroupBuyProduct::getGroupProductInfo($gid, $goods_id);
                        }
                        if ($pin_group_info['delivery_free'] == 1) {
                            $store_freight_total[key($store_final_goods_total)] = 0;
                        }
                    } else {
                        $store_freight_total[key($store_final_goods_total)] = $this->_post_data['pin_info']['store_freight_total'];
                    }
                }
            } elseif (intval($this->_post_data['cycle_num']) > 1) {
                $cycel_info = DcCycleBuyProduct::getCycleProductInfo($goods_list[0]['goods_id'], $this->_post_data['cycle_num']);
                $this->_order_data['cycle_cid'] = $cycel_info['cid'];
                $this->_order_data['cycle_price'] = $cycel_info['cycle_price'];

                if ($transport_id <= 0 && $cycel_info['is_shipping_free'] == 1) {
                    $store_freight_total[key($store_final_goods_total)] = 0;
                } else {
                    $store_freight_total[key($store_final_goods_total)] = bcmul($store_freight_total[key($store_final_goods_total)], $this->_post_data['cycle_num'], 2);
                }
            }

            //重新计算店铺扣除优惠券送商品实际支付金额
            $store_final_goods_total = $this->_logic_buy_1->reCalcGoodsTotal($store_final_goods_total, $input_voucher_list, 'voucher');

            //计算店铺最终订单实际支付金额(加上运费)
            $store_final_order_total = $this->_logic_buy_1->reCalcGoodsTotal($store_final_goods_total, $store_freight_total, 'freight');

            //计算每个店铺(所有店铺级优惠活动，代金券，满减)总共优惠多少
            $store_promotion_total = $this->_logic_buy_1->getStorePromotionTotal($store_goods_total, $store_freight_total, $store_final_order_total);

            //得到有效平台红包
            $input_rpt_info = $this->_logic_buy_1->reParseRptInfo($input_rpt_info, array_sum($store_final_order_total), $this->_member_info['member_id']);

            //计算每个订单应用了多少平台红包
            list($store_final_order_total, $store_rpt_total) = $this->_logic_buy_1->parseOrderRpt($store_final_order_total, $input_rpt_info['rpacket_price']);
            //重新计算优惠金额,将店铺红包减去运费的余额追加到店铺总优惠里
            $store_promotion_total = $this->_logic_buy_1->reCalcStorePromotionTotal($store_promotion_total, $store_freight_total, $store_rpt_total);

            //将赠品追加到购买列表(如果库存0，则不送赠品)
            $append_premiums_to_cart_list = $this->_logic_buy_1->appendPremiumsToCartList($store_cart_list, $store_premiums_list, $store_mansong_rule_list, $this->_member_info['member_id'], $this->_post_data['pintuan']);

            if ($append_premiums_to_cart_list === false) {
                throw new Exception('抱歉，您购买的商品库存不足，请重购买');
            } else {
                list($store_cart_list, $goods_buy_quantity, $store_mansong_rule_list, $goods_sale) = $append_premiums_to_cart_list;
            }

            // 加价购 增加商品销量
            foreach ((array) $this->_order_data['jjgValidSkus'] as $k => $v) {
                foreach ((array) $v as $kk => $vv) {
                    $goods_buy_quantity[$kk] += 1;
                }
            }
        } else {
            //将商品的运费设置从运费模板取
            if ($transport_id > 0) {
                $cancel_calc_sid_list = [];
                foreach ($goods_list as $k => $v) {
                    $goods_list[$k]["transport_id"] = $transport_id;
                    $goods_list[$k]['freight'] = 1;
                    $goods_list[$k]['goods_freight'] = 0;
                }
            } else {
                //取得包邮的店铺ID信息,预定订单只有运费
                $cancel_calc_sid_list = $this->_logic_buy_1->getStoreFreightDescList($store_goods_total);
            }

            //$cancel_calc_sid_list = $this->_logic_buy_1->getStoreFreightDescList($store_goods_total);
            $freight_list = $this->_logic_buy_1->getStoreFreightList($goods_list, array_keys($cancel_calc_sid_list));
            list($store_freight_total, $no_send_tpl_ids) = $this->_logic_buy_1->calcStoreFreight($freight_list, $input_city_id);
            $store_id = $_SESSION['store_id'] ?: $_REQUEST['store_id'] ?: 1;

            // 健康卡会员卡 免运费
            if ($isPhysicalCard > 0) {
                $store_freight_total[$store_id] = 0;
            }
            //计算店铺最终订单实际支付金额(加上运费)
            if ($isPhysicalCard > 0) {
                $store_freight_total[$store_id] = 0;
            }
            $store_final_order_total = $this->_logic_buy_1->reCalcGoodsTotal($store_goods_total, $store_freight_total, 'freight');
            $store_promotion_total = $store_mansong_rule_list = $input_voucher_list = $input_rpt_info = $store_rpt_total = array();
            $goods_buy_quantity = array($goods_list[0]['goods_id'] => $goods_list[0]['goods_num']);
            $goods_sale = array($goods_list[0]['goods_id'] => $goods_list[0]['goods_commonid']);
        }

        if (is_array($no_send_tpl_ids) && !empty($no_send_tpl_ids)) {
            throw new Exception('商品配送未覆盖该地区');
        }

        $bookInfos = [];

        if ($_POST['activity_type'] == 1) {
            foreach ($store_final_order_total as $store_id => $value) {
                $goodInfo = $store_cart_list[$store_id][0];
                // 是定金预售
                if ($goodInfo['activity_type'] == 1) {
                    $bookInfo = $goodInfo['pre_info'];
                    if (($value - $goodInfo['pre_info']['deposit']) < 0.001) {
                        throw new Exception('订单无效，无法结算');
                    }
                    $bookInfo['rest'] = ncPriceFormat($value - $bookInfo['deposit']);
                    $bookInfos[$store_id] = $bookInfo;
                }
            }
        }

        //保存数据
        $this->_order_data['store_goods_total'] = $store_goods_total;
        $this->_order_data['store_final_order_total'] = $store_final_order_total;
        $this->_order_data['store_freight_total'] = $store_freight_total;
        $this->_order_data['store_promotion_total'] = $store_promotion_total;
        $this->_order_data['store_mansong_rule_list'] = $store_mansong_rule_list;
        $this->_order_data['store_cart_list'] = $store_cart_list;
        $this->_order_data['goods_buy_quantity'] = $goods_buy_quantity;
        $this->_order_data['input_voucher_list'] = $input_voucher_list;
        $this->_order_data['input_rpt_info'] = $input_rpt_info;
        $this->_order_data['store_rpt_total'] = $store_rpt_total;
        $this->_order_data['goods_sale'] = $goods_sale;
        $this->_order_data['book_infos'] = $bookInfos;
        $this->_order_data["transport_id"] = $transport_id;
    }

    /**
     * 生成订单
     * @param array $input
     * @throws Exception
     * @return array array(支付单sn,订单列表)
     */
    private function  _createOrderStep4()
    {

        extract($this->_order_data);
        $power_id = $this->_post_data['power_id'];
        $ifcart = $this->_post_data['ifcart'];
        $pintuan = $this->_post_data['pintuan'];
        $source = intval($this->_post_data['source']);
        $store_id = intval($this->_post_data['store_id']);
        $member_id = $this->_member_info['member_id'];
        $member_name = $this->_member_info['member_name'];
        $member_email = $this->_member_info['member_email'];
        $order_member_mobile = $this->_member_info['member_mobile'];

        $model_order = Model('order');
        $model_goods = Model('goods');
        $power_model = Model('power');
        //存储生成的订单数据
        $order_list = array();
        //存储通知信息
        $notice_list = array();
        //拆单信息
        $order_split = array();
        //支付方式
        if ($input_pay_name == 'chain' && $input_chain_id) {
            $store_pay_type_list = array(key($store_cart_list) => 'chain');
        } else {
            //每个店铺订单是货到付款还是线上支付,店铺ID=>付款方式[在线支付/货到付款]
            $store_pay_type_list = $this->_logic_buy_1->getStorePayTypeList(array_keys($store_cart_list), $input_if_offpay, $input_pay_name);
            foreach ($store_pay_type_list as $k => $v) {
                if (empty($input_if_offpay_batch[$k]))
                    $store_pay_type_list[$k] = 'online';
            }
        }

        $pay_sn = $this->_post_data['pay_sn'] ?: $this->_logic_buy_1->makePaySn($member_id);

        $order_pay = array();
        $order_pay['pay_sn'] = $pay_sn;
        $order_pay['buyer_id'] = $member_id;
        if ($pintuan == 2) {
            $order_pay['api_pay_state'] = 1;
        }
        $order_pay_id = $model_order->addOrderPay($order_pay);

        if (!$order_pay_id) {
            throw new Exception('订单保存失败[未生成支付单]');
        }

        //收货人信息
        list($reciver_info, $reciver_name, $reciver_phone) = $this->_logic_buy_1->getReciverAddr($input_address_info);

        // 加价购换购商品 店铺分组
        $jjgValidStoreSkus = array();
        foreach ((array) $this->_order_data['jjgValidSkus'] as $v) {
            foreach ((array) $v as $vv) {
                $jjgValidStoreSkus[$vv['store_id']][] = $vv;
            }
        }

        // 分销员关联客服
        $dis_member_info = [];
        $customer_service_info = [];
        if (C('distribute_isuse') == 1 && $store_id != 3) {
            $model_dis_member_fans = Model('dis_member_fans');
            $dis_member_info = $model_dis_member_fans->getDisMemberInfo($member_id);
            if ($dis_member_info) {
                //异步删除分销关系
                SyncDisMemberQueue::dispatch($member_id);
                // 内部分销员
                if ($dis_member_info['distri_chainid']) {
                    $customer_service_info = Model('chain_bind')->getInfo(['chain_member_mobile' => $dis_member_info['member_mobile']], 'member_id,cash_ratio');
                    if ($customer_service_info && $dis_member_info['dis_member_id'] == $customer_service_info['member_id']) {
                        $customer_service_info = [];
                    }
                }
            }
        } elseif ($store_id == 3 && $_POST['shop_id']) { //福码购分销
            $res = DisDistributor::bindFans($_POST['dis_id'], $member_id, $_POST['shop_id']);
            if (!$res['state']) {
                throw new Exception('分销操作异常');
            }
            if ($res['data']) {
                $dis_member_info = $res['data'];
            }
        }

        $insurance_discount = $insurance_promotion['discount'] ?: 0;

        // 医保商品金额，不参与优惠均摊
        $insurance_total = 0;
        foreach ($store_cart_list[1] as $goods_list) {
            if ($goods_list['vip_state'] == 2) {
                $insurance_total += $goods_list['goods_total'];
            }
        }

        foreach ($store_cart_list as $store_id => $goods_list) {
            //取得本店优惠额度(后面用来计算每件商品实际支付金额，结算需要)
            $promotion_total = !empty($store_promotion_total[$store_id]) ? $store_promotion_total[$store_id] : 0;
            $promotion_total -= $insurance_discount;

            //本店总的优惠比例,保留3位小数
            $should_goods_total = $store_goods_total[$store_id] - $insurance_total;
            if ($should_goods_total > 0) {
                $promotion_rate = $promotion_total / $should_goods_total;
                if ($promotion_rate > 1) {
                    $promotion_rate = 0;
                }
            } else {
                $promotion_rate = 0;
            }

            //每种商品的优惠金额累加保存入 $promotion_sum
            $promotion_sum = 0;

            $order = array();
            $order_common = array();
            //保存促销信息
            $order_common['promotion_info'] = array();

            $order_goods = array();

            $order['order_sn'] = $this->_logic_buy_1->makeOrderSn($order_pay_id);
            $order['pay_sn'] = $pay_sn;
            $order['store_id'] = $store_id;
            $order['store_name'] = $goods_list[0]['store_name'];
            $order['buyer_id'] = $member_id;
            $order['buyer_name'] = $member_name;
            $order['buyer_email'] = $member_email;
            $order['encrypt_mobile'] = base64_encode(rc4($order_member_mobile));
            $order['buyer_phone'] = mobile_star($order_member_mobile);
            $order['add_time'] = TIMESTAMP;
            $order['payment_code'] = $store_pay_type_list[$store_id];
            $order['order_state'] = $store_pay_type_list[$store_id] == 'offline' ? ORDER_STATE_PAY : ORDER_STATE_NEW;
            $order['order_amount'] = $store_final_order_total[$store_id];
            $order['shipping_fee'] = $store_freight_total[$store_id];
            $order['goods_amount'] = $order['order_amount'] - $order['shipping_fee'] + $store_rpt_total[$store_id];
            $order['order_type'] = $input_chain_id ? 3 : ($goods_list[0]['is_book'] ? 2 : 1);

            $power_state = true;
            if ($power_id && !$ifcart) {
                $power_info = $power_model->getPowerInfo(['power_id' => $power_id, 'power_status' => 1]);
                if ($power_info) {
                    //保存数据
                    $power_state = false;
                    $order['order_type'] = 99;

                    //如果不是偏远地区，就把运费设置为0
                    if ($transport_id <= 0) {
                        $order['shipping_fee'] = 0;
                    }

                    $order['order_amount'] = $power_info['power_price'] + $order['shipping_fee'];
                    $order['goods_amount'] = $power_info['power_price'] + $order['shipping_fee'];
                }
            }

            $order['order_from'] = $order_from;
            if ($goods_list[0]['pintuan_promotion']) $order['order_type'] = 4;
            $order['chain_id'] = (($input_chain_id ? $input_chain_id : 0) ? $input_chain_id : $this->_post_data['chain_id']) ? $this->_post_data['chain_id'] : 0;
            $order['rpt_amount'] = empty($store_rpt_total[$store_id]) ? 0 : $store_rpt_total[$store_id];
            $order['dis_type'] = $this->_post_data['dis_type'] ?: 5;
            $order['is_live'] = $this->_post_data['is_live'];
            $order['first_order'] = $this->_post_data['first_order'];

            if ($_POST['activity_type'] == 2) {
                $order['order_type'] = 10; // 新人专享
            } elseif ($bookInfo = $book_infos[$store_id]) {
                $order['order_type'] = 11; // 定金预售
            } elseif ($_POST['activity_type'] == 19) {
                $order['order_type'] = 19; // 0元购
                $order_common['promotion_info'][] = ['开卡礼包0元领', $order['order_amount']];
                $order['order_amount'] = 0;
                $order['order_state'] = ORDER_STATE_PAY;
            } elseif ($pintuan == 2) { //拼团成功
                $order['order_type'] = 4;
                $order['trade_no'] = $this->_post_data['trade_no'];
                $order['payment_time'] = $this->_post_data['payment_time'];
                $order['add_time'] = $this->_post_data['add_time'];
                $order['order_state'] = 20;
                $order['order_sn'] = $this->_post_data['order_sn'];
                $order['payment_from']  = C('dianyin_pay') ? 1 : 0; // 支付来源
                $order['payment_code']  =  ($this->_post_data['pay_type'] == 8 ? 'card' : ($this->_post_data['pay_type'] == 1 ? 'ali_native' : 'wx_jsapi')); // 支付方式
                //获取运费
                $order['shipping_fee'] = $this->_post_data['pin_info']['store_freight_total'];
                $order['order_amount'] = $this->_post_data['order_amount'];
            } elseif (intval($this->_post_data['cycle_num']) > 1) { //周期购
                // 周期购主单因为不推订单中心，要的单独检查黑名单
                $unlockTime = RiskUser::whereIn('mobile', [$order_member_mobile, $reciver_phone])
                    ->whereRaw('status = 1 and unlock_time > now()')
                    ->order('unlock_time desc')
                    ->value('unlock_time');

                if ($unlockTime) {
                    // 不存在白名单
                    if (empty(RiskWhitelist::where('mobile = ? and status = 1', [$order_member_mobile])->value('id'))) {
                        $seconds = strtotime($unlockTime) - time();
                        $notice = "账号存在异常，已被管控，本次交易无法完成。账号将在%s后解封。";
                        if ($seconds > 24 * 60 * 60) {
                            throw new Exception(sprintf($notice, ceil($seconds / (24 * 60 * 60)) . '天'));
                        } elseif ($seconds > 60 * 60) {
                            throw new Exception(sprintf($notice, ceil($seconds / (60 * 60)) . '小时'));
                        } elseif ($seconds > 60) {
                            throw new Exception(sprintf($notice, ceil($seconds / 60) . '分钟'));
                        } else {
                            throw new Exception(sprintf($notice, $seconds . '秒'));
                        }
                    }
                }
                $order['order_type'] = 9;
                $order['is_head'] = 1;
                $order['order_father'] = 1;
                $order['cycle_num'] = $this->_post_data['cycle_num'];
            } elseif ($this->isHospital()) {
                $order['order_type'] = 13; // 互联网医院
            }
            //微信广告标识
            $order['click_id'] = $_SERVER['HTTP_CLICK_ID'] ?: 0;
            // 下单是否使用虚拟库存
            $order['is_use_virtual_stock'] = $goods_list[0]['is_open_virtual_stock'] ? 1 : 0;
            // 健康会员卡 实体卡 订单 则order_type=21
            if ($model_goods->isPhysicalCard([$goods_list[0]['goods_id']])[$goods_list[0]['goods_id']] > 0) {
                $order['order_type'] = 21;
            }
            $order_id = $model_order->addOrder($order);
            if (!$order_id) {
                throw new Exception('订单保存失败[未生成订单数据]');
            }
            $order['order_id'] = $order_id;
            $order_list[$order_id] = $order;

            $order_common['order_id'] = $order_id;
            $order_common['store_id'] = $store_id;
            $order_common['order_message'] = isset($input_pay_message[$store_id]) ? $input_pay_message[$store_id] : '';

            //代金券
            if (isset($input_voucher_list[$store_id])) {
                $order_common['voucher_price'] = $input_voucher_list[$store_id]['voucher_price'];
                $order_common['voucher_code'] = $input_voucher_list[$store_id]['voucher_code'];
            }

            //订单总优惠金额（代金券，满减，平台红包）
            if ($_POST['activity_type'] == 19) {
                $order_common['promotion_total'] = $store_final_order_total[$store_id];
            } else {
                $order_common['promotion_total'] = $promotion_total + $insurance_discount;
            }
            $order_common['reciver_info'] = $reciver_info;
            $order_common['reciver_name'] = $reciver_name;
            $order_common['reciver_city_id'] = $input_city_id;

            //发票信息
            $order_common['invoice_info'] = $this->_logic_buy_1->createInvoiceData($input_invoice_info);

            if (is_array($store_mansong_rule_list[$store_id])) {
                if (APP_ID != 'mobile') {
                    $order_common['promotion_info'][] =  array('满即送', $store_mansong_rule_list[$store_id]['desc']);
                } else {
                    $order_common['promotion_info'][] =  array('满即送', $store_mansong_rule_list[$store_id]['desc']['desc']);
                }
            }

            //平台红包值
            if ($store_rpt_total[$store_id]) {
                $order_common['promotion_info'][] = array('平台红包', sprintf('使用%s元红包 编码：%s', $store_rpt_total[$store_id], $input_rpt_info['rpacket_code']));
            }

            //代金券
            if (isset($input_voucher_list[$store_id])) {
                $_voucher_type = $input_chain_id ? '门店代金券' : '店铺代金券';
                $order_common['promotion_info'][] = array(
                    $_voucher_type,
                    sprintf('使用%s元代金券 编码：%s', $input_voucher_list[$store_id]['voucher_price'], $input_voucher_list[$store_id]['voucher_code']),
                    $input_voucher_list[$store_id]['voucher_t_id']
                );
            }
            $order_common['promotion_info'] = $order_common['promotion_info'] ? serialize($order_common['promotion_info']) : '';
            $order_common['reciver_date_msg'] = $_POST['reciver_date_msg'];

            $insert = $model_order->addOrderCommon($order_common);
            if (!$insert) {
                throw new Exception('订单保存失败[未生成订单扩展数据]');
            }

            // 插入定金预售信息
            if ($bookInfo) {
                $insert = Model()->table('order_presale')->insert([
                    'parent_order_sn' => $order['order_sn'],
                    'erp_order_sn' => $order['order_sn'],
                    'pre_status' => OrderPresale::STATUS_UNPAID,
                    'pre_price' => $bookInfo['deposit'],
                    'last_price' => $bookInfo['rest'],
                    'pre_start_time' => $bookInfo['deposit_begin_date'],
                    'pre_end_time' => $bookInfo['deposit_end_date'],
                    'last_start_time' => $bookInfo['rest_begin_date'],
                    'last_end_time' => $bookInfo['rest_end_date']
                ]);

                if (!$insert) {
                    throw new Exception('订单保存失败[未生成定金数据]');
                }
            }

            //添加订单日志
            $log_data = array();
            $log_data['order_id'] = $order_id;
            $log_data['log_role'] = '买家';
            $log_data['log_msg'] = '生成订单';
            $log_data['log_user'] = $member_name;
            $log_data['log_orderstate'] = ORDER_STATE_NEW;
            $model_order->addOrderLog($log_data);

            // 生成order_goods订单商品数据
            $goods_model = Model("goods");
            $i = 0;
            $order_is_dis = 0; //是否分销订单
            $order_dis_member_id = 0;
            $dis_order_amount = 0;
            foreach ($goods_list as $goods_info) {
                if (!intval($goods_info['bl_id'])) {
                    //如果不是优惠套装
                    $goods_commonid = $goods_info['goods_commonid'];
                    $order_goods[$i]['order_id'] = $order_id;
                    $order_goods[$i]['goods_id'] = $goods_info['goods_id'];
                    $order_goods[$i]['store_id'] = $store_id;
                    $order_goods[$i]['goods_name'] = $goods_info['goods_name'];
                    $order_goods[$i]['goods_price'] = $goods_info['goods_price'];
                    $order_goods[$i]['goods_original_price'] = $goods_info['goods_original_price'] ?: 0;
                    $order_goods[$i]['goods_num'] = $goods_info['goods_num'];
                    $order_goods[$i]['goods_image'] = $goods_info['goods_image'];
                    $order_goods[$i]['goods_spec'] = $goods_info['goods_spec'];
                    $order_goods[$i]['buyer_id'] = $member_id;
                    $order_goods[$i]['goods_commonid'] = $goods_commonid;
                    $order_goods[$i]['add_time'] = TIMESTAMP;

                    // 虚拟库存扣减
                    if ($goods_info['is_open_virtual_stock']) {
                        $order_goods[$i]['is_use_virtual_stock'] = 1;
                    }

                    $order_goods[$i]['sku'] = $goods_info['goods_sku'];

                    //是否存在APP推荐ID
                    if (isset($goods_info['app_order_id'])) {
                        $order_goods[$i]['app_order_id'] = $goods_info['app_order_id'];
                    }
                    if ($goods_info['ifgroupbuy']) {
                        $ifgroupbuy = true;
                        $order_goods[$i]['goods_type'] = 2;
                    } elseif ($goods_info['promotion_info']['promotion_type'] == 2) { //类型:2限时折扣,3秒杀,4闪购
                        if ($goods_info['is_member_price']) {
                            $order_goods[$i]['goods_type'] = $goods_info['is_vip_discount'] ? 16 : 12;
                        } else {
                            $order_goods[$i]['goods_type'] = 3;
                        }
                    } elseif ($goods_info['promotion_info']['promotion_type'] == 3) {
                        $order_goods[$i]['goods_type'] = 6;
                        $_n = $model_order->getSpikeOrderLimit($member_id, $goods_info);
                        if ($_n > 0) throw new Exception('抱歉，秒杀商品只能购买' . $_n . '次，请重新选择');
                    } elseif ($goods_info['promotion_info']['promotion_type'] == 4) {
                        $order_goods[$i]['goods_type'] = 7;
                    } elseif ($goods_info['ifzengpin']) {
                        $order_goods[$i]['goods_type'] = 5;
                    } elseif ($goods_info['jjgRank'] > 0) {
                        // 加价购活动参与商品
                        $order_goods[$i]['goods_type'] = 8;
                    } elseif ($goods_model->isPhysicalCard([$goods_info["goods_id"]])[$goods_info["goods_id"]] > 0) {
                        $order_goods[$i]['goods_type'] = 21;
                    } else {
                        $order_goods[$i]['goods_type'] = 1;
                        //会员价促销级别最低
                        if ($goods_info['is_member_price'] == 1) {
                            $order_goods[$i]['goods_type'] = $goods_info['is_vip_discount'] ? 16 : 12;
                        }
                    }
                    if ($goods_info['pintuan_promotion']) { // 增加拼团活动id记录lihaobin 2019--4-21
                        $order_goods[$i]['goods_type'] = 10;
                    }

                    $order_goods[$i]['chain_id'] = $this->_post_data['chain_id'] ?: 0; //增加门店id记录 lihaobin 2019-09-02
                    if ($input_chain_id && !$goods_info['ifzengpin']) {
                        $order_goods[$i]['goods_type'] = 1;
                    }

                    $order_goods[$i]['promotions_id'] = $goods_info['promotions_id'] ?: 0;
                    $order_goods[$i]['commis_rate'] = 200;
                    $order_goods[$i]['gc_id'] = $goods_info['gc_id'];

                    //记录消费者保障服务
                    $contract_itemid_arr = $goods_info['contractlist'] ? array_keys($goods_info['contractlist']) : array();
                    $order_goods[$i]['goods_contractid'] = $contract_itemid_arr ? implode(',', $contract_itemid_arr) : '';

                    if ($_POST['activity_type'] == 19) {
                        $order_goods[$i]['goods_pay_price'] = 0;
                        $cg = $goods_info['card_gift'];
                        $rs = Model()->table('datacenter.vip_user_equity_record')->insert([
                            'order_sn' => $cg['order_sn'],
                            'equity_id' => $cg['equity_id'],
                            'privilege_id' => $goods_info['goods_id'],
                            'gift_order_sn' => $order['order_sn'],
                        ]);
                        if (!$rs) {
                            throw new Exception('插入礼包领取记录出错');
                        }
                    } else if ($goods_info['vip_state'] == 2) {
                        $order_goods[$i]['goods_pay_price'] = bcsub($goods_info['goods_total'], $goods_info['insurance_discount'], 2);
                    } else {
                        //计算商品金额
                        $goods_total = bcmul($goods_info['goods_price'], $goods_info['goods_num'], 2);
                        //计算本件商品优惠金额
                        //                        $promotion_value = bcdiv(ceil($goods_total*$promotion_rate*100),100,2);
                        // 计算促销价值
                        $promotion_value = number_format($goods_total * $promotion_rate, 2, '.', '');
                        $order_goods[$i]['goods_pay_price'] = $goods_total - $promotion_value < 0 ? 0 : bcsub($goods_total, $promotion_value, 2);
                        $promotion_sum += $promotion_value;
                    }

                    $order_goods[$i]['is_dis'] = 0;
                    $order_goods[$i]['dis_member_id'] = 0;
                    $order_goods[$i]['dis_commis_rate'] = 0;

                    $goods_common_info = $model_goods->getGoodsInfoByID($goods_info['goods_id'], 'is_dis,dis_commis_rate');
                    $goods_info['is_dis'] = $goods_common_info['is_dis'];
                    //订单分销判断
                    $dis_member_id = $dis_member_info['dis_member_id'] ?: 0;
                    $outside_member_id = $dis_member_info['outside_member_id'] ?: 0;
                    //分销商品佣金比例
                    if (C('distribute_isuse') == 1 && $goods_info['is_dis'] == 1 && $dis_member_id > 0 && $order_goods[$i]['goods_pay_price'] >= 1) {
                        $order_is_dis = 1;
                        $order_goods[$i]['is_dis'] = 1;
                        $order_goods[$i]['dis_commis_rate'] = $goods_common_info['dis_commis_rate'];
                        $order_dis_member_id = $order_goods[$i]['dis_member_id'] = $dis_member_id;
                        if ($store_id == 3) { //福码购分销
                            $order_goods[$i]['shop_id'] = $dis_member_info['shop_id'];
                            $goods_pay_price = ncPriceFormat($order_goods[$i]['goods_pay_price']);
                            $dis_order_amount += $this->intval_format_amount($goods_pay_price * 100);
                        } else {
                            if ($customer_service_info) {
                                $order_goods[$i]['customer_service_id'] = $customer_service_info['member_id'];
                                $order_goods[$i]['customer_service_rate'] = $customer_service_info['cash_ratio'];
                            }
                            $order_goods[$i]['out_member_id'] = $outside_member_id;
                            if ($dis_member_info['distri_chainid']) {
                                $order_goods[$i]['chain_id'] = $dis_member_info['distri_chainid'];
                            }
                        }
                    }
                    $order_goods[$i]['is_live'] = $goods_info['is_live'] ?: 0;
                    $order_goods[$i]['is_group_goods'] = $goods_info['is_group_goods'];
                    $i++;

                    //存储库存报警数据
                    if ($goods_info['goods_storage_alarm'] >= ($goods_info['goods_storage'] - $goods_info['goods_num'])) {
                        $param = array();
                        $param['common_id'] = $goods_info['goods_commonid'];
                        $param['sku_id'] = $goods_info['goods_id'];
                        $notice_list['goods_storage_alarm'][$goods_info['store_id']] = $param;
                    }
                    if ($goods_info['promotion_info']['promotion_type'] == 3) { //类型:3秒杀
                        Model('p_spike_goods')->editSpikeGoods(array('had_spiked_count' => array('exp', 'had_spiked_count+' . $goods_info['goods_num'])), array('spike_goods_id' => $goods_info['promotion_info']['spike_goods_id']));
                    }
                    if ($goods_info['promotion_info']['promotion_type'] == 4) { //类型:4闪购
                        Model('p_flash_goods')->editFlashGoods(array('buy_count' => array('exp', 'buy_count+' . $goods_info['goods_num'])), array('flash_goods_id' => $goods_info['promotion_info']['flash_goods_id']));
                    }
                }
            }

            //将因舍出小数部分出现的差值补到最后一个商品的实际成交价中(商品goods_price=0时不给补，可能是赠品)
            if (abs($promotion_total - $promotion_sum) > 0.001) {
                for ($key = count($order_goods) - 1; $key >= 0; $key--) {
                    if (floatval($order_goods[$key]['goods_price']) > 0 && $order_goods[$k]['goods_type'] != 17) {
                        $order_goods[$key]['goods_pay_price'] -= $promotion_total - $promotion_sum;
                        break;
                    }
                }
            }
            $insert = $model_order->addOrderGoodsOne($order_goods);
            if (empty($insert)) {
                throw new Exception('订单保存失败[未生成商品数据01]');
            }
            $insert_id = $insert;

            $order_data = [];
            if ($store_id == 3) { //福码购分销
                if ($order_is_dis) {
                    $order_data['dis_type'] = $_POST['dis_type'] == '' ? 5 : intval($_POST['dis_type']);
                    $order_data['is_dis'] = 1;
                    $order_data['tuoke_salesperson_id'] = $dis_member_info['tuoke_salesperson_id'];
                }
                $dis_member_info['order_amount'] = $dis_order_amount;
                $dis_member_info['order_id'] = $order_id;
                $dis_member_info['order_sn'] = $order['order_sn'];
                $dis_member_info['type'] = 1;
            } else {
                /**@var distributeLogic $distribute*/
                $distribute = Logic('distribute');
                if ($order_is_dis) {
                    //如果是自己扫自己的码购买，不计算佣金，只算业绩
                    if ($member_id == $dis_member_id) {
                        $order_data['dis_type'] = 3;
                    }
                    $order_data['is_dis'] = $order_is_dis;
                    $order_data['chain_id'] = $distribute->getDisMemberChain($order_dis_member_id);
                } else if (intval($_POST['source']) == 0) {
                    //如果有绑定关系，但是商品不是分销商品，那么也算门店订单 lihaobin 2020-02-10
                    $order_data['is_dis'] = 0;
                    $order_data['chain_id'] = $distribute->getDisMemberChain($dis_member_id);
                }
            }
            $model_order->editOrder($order_data, array('order_id' => $order_id));
            //修改助力下单成功回填订单号
            if (!$power_state) {
                $power_model->editPower(['order_id' => $order_id, 'power_status' => 2], ['power_id' => $power_id]);
            }
            //存储商家发货提醒数据
            if ($store_pay_type_list[$store_id] == 'offline') {
                $notice_list['new_order'][$order['store_id']] = array('order_sn' => $order['order_sn']);
            }

            if ($_POST['activity_type'] != 19) {
                $dc_goods_total = $order['goods_amount'] + $order_common['promotion_total'];
                $order_split['order']['freight'] = $order['shipping_fee'] ? $this->intval_format_amount($order['shipping_fee'] * 100) : 0; //总运费
                $order_split['order']['privilege'] = $order_common['promotion_total'] ? $this->intval_format_amount($order_common['promotion_total'] * 100) : 0; //总优惠金额
            } else {
                $dc_goods_total = $order['goods_amount'];
                $order_split['order']['freight'] = 0; //总运费
                $order_split['order']['privilege'] = $order_common['promotion_total'] ? $this->intval_format_amount(($order_common['promotion_total'] - $order['shipping_fee']) * 100) : 0; //总优惠金额
            }

            $order_split['order']['old_order_sn'] = $order['order_sn']; //订单编号
            $order_split['order']['buyer_memo'] = $order_common['order_message']; //买家留言
            $order_split['order']['channel_id'] = 5; //渠道ID：1阿闻到家,2美团,3饿了么,4京东到家,5阿闻电商,6门店,7互联网医疗
            $order_split['order']['expected_time'] = ""; //预计送达时间
            $order_split['order']['goods_total'] = $order['goods_amount'] ? $this->intval_format_amount($dc_goods_total * 100) : 0; //商品总金额
            $order_split['order']['invoice'] = $this->_logic_buy_1->createInvoiceData($input_invoice_info); //发票信息
            $order_split['order']['is_split'] = 0; //是否有拆单，0否1是
            $order_split['order']['is_virtual'] = 0; //是否是虚拟订单，0否1是
            $order_split['order']['latitude'] = 0; //收货地址纬度
            $order_split['order']['longitude'] = 0; //收货地址经度
            $order_split['order']['order_type'] = $order['order_type']; //订单类型1普通订单(默认),2预定订单,3门店自提(电商订单专用),4拼团订单,5门店配送,6健康计划,7保险订单,8积分订单,9周期购 99助力

            $datacenter_address_info = unserialize($order_common['reciver_info']);
            $datacenter_area_info_arr = explode(" ", $datacenter_address_info['area']);
            $order_split['order']['receiver_address'] = $datacenter_address_info['address']; //收件地址
            $order_split['order']['receiver_state'] = $datacenter_area_info_arr[0] ? $datacenter_area_info_arr[0] : ""; //收件省
            $order_split['order']['receiver_city'] = $datacenter_area_info_arr[1] ? $datacenter_area_info_arr[1] : ""; //收件市
            $order_split['order']['receiver_district'] = $datacenter_area_info_arr[2] ? $datacenter_area_info_arr[2] : ""; //收件区
            $order_split['order']['receiver_name'] = $order_common['reciver_name']; //收件人
            $order_split['order']['receiver_phone'] = $reciver_phone; //收件电话
            $order_split['order']['shop_id'] = (string)$order['store_id']; //商户或门店id
            $order_split['order']['shop_name'] = $order['store_name']; //商户名称
            $order_split['order']['total'] = $order['order_amount'] ? $this->intval_format_amount($order['order_amount'] * 100) : 0; //总金额（付款金额，加上运费，减优惠金额）
            $order_split['order']['user_agent'] = $source ? order_source($source) : order_source($_POST['source']); //渠道来源,1-Android,2-iOS,3-小程序,4-公众号,5-Web,6-其它 7-竖屏
            $order_split['order']['power_id'] = $power_id; //助力订单

            $order_split['order']['order_pay_type'] = $order_data['is_dis'] ?
                OrderAlias::ORDER_PAY_TYPE_DIS : OrderAlias::ORDER_PAY_TYPE_NOT_DIS;

            $order_split['order_products'] = $order_goods;
            foreach ($order_split['order_products'] as $k => $v) {
                $goods_info_type = $model_goods->getGoodsInfoById($v['goods_id']);
                $goods_sku_type = intval($goods_info_type['goods_sku_type']);
                if ($v['is_group_goods'] > 0) { //组合商品
                    $v['group_goods_list'] = GoodsGroup::getGroupGoodsList(array('goods_type' => $v['is_group_goods'], 'goods_commonid' => $v['goods_commonid'])); //$goods_info['group_goods_list'];
                    Logic('goods')->recalculateSubGoodsPrice($v);
                    $child_goods_list = $v['group_goods_list'];

                    if (is_array($child_goods_list) && !empty($child_goods_list)) {
                        foreach ($child_goods_list as $kk => $vv) {
                            $vv_goods_info = $model_goods->getGoodsInfo(array("goods_commonid" => $vv['goods_commonid']), "goods_id,goods_sku_type,goods_barcode,goods_image,goods_spec,goods_sku,warehouse_type");
                            $vv_goods_sku_type = intval($vv_goods_info['goods_sku_type']);
                            $order_split['order_products'][$k]['child_product_list'][$kk]['discount_count'] = 0;
                            $order_split['order_products'][$k]['child_product_list'][$kk]['discount_price'] = $this->intval_format_amount($vv['discount_value'] * 100);
                            $order_split['order_products'][$k]['child_product_list'][$kk]['image'] = isset($vv['goods_image_url']) ? (string)$vv['goods_image_url'] : "";;
                            //$order_split['order_products'][$k]['child_product_list'][$kk]['is_have_reality']    = "";
                            $order_split['order_products'][$k]['child_product_list'][$kk]['specs'] = isset($vv_goods_info['goods_spec']) ? (string)$vv_goods_info['goods_spec'] : "";
                            $order_split['order_products'][$k]['child_product_list'][$kk]['number'] = intval($vv['goods_number']);
                            $order_split['order_products'][$k]['child_product_list'][$kk]['parent_sku'] = isset($v['goods_commonid']) ? (string)$v['goods_commonid'] : "";
                            $order_split['order_products'][$k]['child_product_list'][$kk]['price'] = $this->intval_format_amount($vv['goods_price'] * 100);
                            $order_split['order_products'][$k]['child_product_list'][$kk]['product_id'] = isset($vv_goods_info['goods_id']) ? (string)$vv_goods_info['goods_id'] : "";
                            $order_split['order_products'][$k]['child_product_list'][$kk]['product_name'] = (string)$vv['goods_name'];
                            $order_split['order_products'][$k]['child_product_list'][$kk]['product_type'] = 1;
                            $order_split['order_products'][$k]['child_product_list'][$kk]['promotion_id'] = 0;
                            $order_split['order_products'][$k]['child_product_list'][$kk]['promotion_type'] = 0;
                            $order_split['order_products'][$k]['child_product_list'][$kk]['source'] = $vv_goods_sku_type ? $vv_goods_sku_type : 1;
                            $order_split['order_products'][$k]['child_product_list'][$kk]['bar_code'] = $vv_goods_info['goods_barcode'];
                            $order_split['order_products'][$k]['child_product_list'][$kk]['sku'] = isset($vv_goods_info['goods_id']) ? (string)$vv_goods_info['goods_id'] : ""; //isset($vv_goods_info['goods_sku']) ? (string)$vv_goods_info['goods_sku'] : "";
                            $order_split['order_products'][$k]['child_product_list'][$kk]['rec_id'] = 0;
                            $order_split['order_products'][$k]['child_product_list'][$kk]['combine_type'] = 0;
                            $order_split['order_products'][$k]['child_product_list'][$kk]['child_product_list'] = [];
                            $order_split['order_products'][$k]['child_product_list'][$kk]['term_type'] = 0;
                            $order_split['order_products'][$k]['child_product_list'][$kk]['term_value'] = 0;
                            $order_split['order_products'][$k]['child_product_list'][$kk]['warehouse_type'] = (int)$vv['warehouse_type'];
                        }
                    }
                } else {
                    $order_split['order_products'][$k]['child_product_list'] = [];
                }
                $order_split['order_products'][$k]['discount_count'] = 0; // 参与限时折扣的商品数量
                $order_split['order_products'][$k]['discount_price'] = $this->intval_format_amount($v['goods_pay_price'] * 100); // 折扣价格
                $order_split['order_products'][$k]['image'] = isset($v['goods_image']) ? (string)$v['goods_image'] : ""; // 商品图片
                //$order_split['order_products'][$k]['is_have_reality'] = $v['is_group_goods'] > 0 ? 1 : 0;// 针对组合商品，是否有实物商品（0-没有1-有）
                $order_split['order_products'][$k]['specs'] = isset($v['goods_spec']) ? (string)$v['goods_spec'] : "";
                $order_split['order_products'][$k]['number'] = intval($v['goods_num']); // 数量
                $order_split['order_products'][$k]['parent_sku'] = ""; // 组合商品父级sku
                $order_split['order_products'][$k]['price'] = $this->intval_format_amount($v['goods_price'] * 100); // 单价
                $order_split['order_products'][$k]['product_id'] = isset($v['goods_id']) ? (string)$v['goods_id'] : ""; //(string)$v['goods_id'];// 商品id
                $order_split['order_products'][$k]['product_name'] = (string)$v['goods_name']; // 商品名称
                $order_split['order_products'][$k]['product_type'] = $v['is_group_goods'] > 0 ? 3 : 1; // 商品类型1-实物商品，2-虚拟商品，3-组合商品
                $order_split['order_products'][$k]['promotion_id'] = isset($v['promotions_id']) ? intval($v['promotions_id']) : 0; // 促销活动Id
                $order_split['order_products'][$k]['promotion_type'] = 0; // 活动类型1-满减商品2限时折扣3-满减运费
                $order_split['order_goods'][$k]['source'] = $goods_sku_type ? $goods_sku_type : 1;
                $order_split['order_products'][$k]['bar_code'] = $goods_info_type['goods_barcode'] ? $goods_info_type['goods_barcode'] : "";
                $order_split['order_products'][$k]['sku'] = isset($v['goods_id']) ? (string)$v['goods_id'] : "";
                $order_split['order_products'][$k]['rec_id'] = $insert_id[$k] ? $insert_id[$k] : 0;
                $order_split['order_products'][$k]['combine_type'] = intval($this->getCombineType($v['is_group_goods'])); //组合商品组合类型0-非组合31-实物实物32-实物虚拟33-虚拟虚拟
                $order_split['order_products'][$k]['term_type'] = 0;
                $order_split['order_products'][$k]['term_value'] = 0;
                $order_split['order_products'][$k]['warehouse_type'] = (int)$goods_info_type['warehouse_type'];
                $order_split['order_products'][$k]['use_virtual_stock'] = (int)$v['is_use_virtual_stock'];
            }
            $order_split['order_promotions'] = [];
            $order_split['pay_info'] = (object)[];

            unset($order_split['order_goods']);
            //周期购订单拓展信息
            if ($order['order_type'] == 9 && $this->_post_data['cycle_num'] > 1) {
                $model_cycle = Model('cycle_push_info');
                $push_data['order_sn'] = $order['order_sn'];
                $push_data['parent_order_sn'] = $order['order_sn'];
                $push_data['is_head'] = 1;
                $push_data['cycle_price'] = isset($cycle_price) ? $cycle_price : 0;
                $push_data['cid'] = isset($cycle_cid) ? $cycle_cid : 0;
                $push_data['member_id'] = $member_id;
                $push_data['cycle_num'] = $this->_post_data['cycle_num'];
                $push_data['delivery_interval'] = $this->_post_data['delivery_interval'];
                $cycle_list = $model_order->getCycleProductByDeliveryTime($push_data['cycle_num'], $push_data['delivery_interval']);
                $cycle_list = array_column($cycle_list, 'date');
                $push_data['push_date'] = implode(',', $cycle_list);
                $push_data['push_param'] = json_encode($order_split, JSON_UNESCAPED_UNICODE);
                $push_data['promotion_total'] = $order_split['order']['privilege'];
                $cyclel_res = $model_cycle->addCyclePushInfo($push_data);
                if (empty($cyclel_res)) {
                    throw new Exception('周期购订单保存失败');
                }

                //更新周期购商品表订单数量
                $cart_ids = explode(',', $_POST['cart_id']);
                $cart_id = explode('|', $cart_ids[0]);
                $sku_id = $cart_id[0];
                $time = date('Y-m-d H:i:s');
                (new DcCycleBuyProduct())->updateCycleBuyProduct($sku_id, $time);
            }

            if ($order['order_type'] != 9 && !$this->_post_data['ignore_push']) {
                //推送订单到数据中台
                $rs =  $this->push_datacenter_order($order_split, $order['encrypt_mobile']);
                if ($rs['order_sn']) {
                    $model_order->editOrder([
                        'erp_order_id' => $rs['order_sn']
                    ], array('order_id' => $order_id));
                }
            }

            if (($order['order_type'] == 4 && $pintuan == 2) || $order['order_type'] == 19) { //拼团订单
                SyncDatacenterOrderPayQueue::dispatch($order, $order, 1, $order['encrypt_mobile']);
            }

            // 订单推送到视频号，不能异步处理，因为可能立即请求支付接口
            // 拼团、积分、周期购、预售、秒杀不推送
            if ($order['is_live'] == 2 && !in_array($order['order_type'], [4, 8, 9, 11, 12])) {
                $data = action(new MixTransformToMiniShopAction($order));
                SyncTask::createAndRun(
                    $data,
                    'mini_program',
                    'shop_order_add'
                );
            }
        }

        //保存数据
        $this->_order_data['pay_sn'] = $pay_sn;
        $this->_order_data['order_sn'] = $order['order_sn'];
        $this->_order_data['order_list'] = $order_list;
        $this->_order_data['notice_list'] = $notice_list;
        $this->_order_data['ifgroupbuy'] = $ifgroupbuy;
        $this->_order_data['ifbook'] = $goods_list[0]['is_book'] == 1;
        $this->_order_data['order_id'] = $order_id;
        $this->_order_data['dis_member_info'] = $dis_member_info; //用于福码购分销记录下一步使用
    }

    /**
     * 生成互联网医疗订单
     * 复制_createOrderStep4修改后的方法
     * @param array $input
     * @throws Exception
     * @return array array(支付单sn,订单列表)
     */
    private function  _createOrderForHospital()
    {
        extract($this->_order_data);
        $power_id = $this->_post_data['power_id'];
        $ifcart = $this->_post_data['ifcart'];
        $member_id = $this->_member_info['member_id'];
        $member_name = $this->_member_info['member_name'];
        $member_email = $this->_member_info['member_email'];
        $order_member_mobile = $this->_member_info['member_mobile'];

        $model_order = Model('order');
        $model_goods = Model('goods');
        $power_model = Model('power');
        //存储生成的订单数据
        $order_list = array();
        //存储通知信息
        $notice_list = array();
        //拆单信息
        $order_split = array();
        //支付方式
        if ($input_pay_name == 'chain' && $input_chain_id) {
            $store_pay_type_list = array(key($store_cart_list) => 'chain');
        } else {
            //每个店铺订单是货到付款还是线上支付,店铺ID=>付款方式[在线支付/货到付款]
            $store_pay_type_list = $this->_logic_buy_1->getStorePayTypeList(array_keys($store_cart_list), $input_if_offpay, $input_pay_name);
            foreach ($store_pay_type_list as $k => $v) {
                if (empty($input_if_offpay_batch[$k]))
                    $store_pay_type_list[$k] = 'online';
            }
        }

        $pay_sn = $this->_post_data['pay_sn'] ?: $this->_logic_buy_1->makePaySn($member_id);

        $order_pay = array();
        $order_pay['pay_sn'] = $pay_sn;
        $order_pay['buyer_id'] = $member_id;
        $order_pay_id = $model_order->addOrderPay($order_pay);

        if (!$order_pay_id) {
            throw new Exception('订单保存失败[未生成支付单]');
        }

        //收货人信息
        list($reciver_info, $reciver_name, $reciver_phone) = $this->_logic_buy_1->getReciverAddr($input_address_info);

        $insert_id = [];
        foreach ($store_cart_list as $store_id => $goods_list) {
            //取得本店优惠额度(后面用来计算每件商品实际支付金额，结算需要)
            $promotion_total = !empty($store_promotion_total[$store_id]) ? $store_promotion_total[$store_id] : 0;

            //本店总的优惠比例,保留3位小数
            $should_goods_total = $store_goods_total[$store_id];
            $promotion_rate = $should_goods_total > 0 ? abs(number_format($promotion_total / $should_goods_total, 5)) : 0;
            if ($promotion_rate <= 1) {
                $promotion_rate = floatval(substr($promotion_rate, 0, 5));
            } else {
                $promotion_rate = 0;
            }
            //每种商品的优惠金额累加保存入 $promotion_sum
            $promotion_sum = 0;

            $order = array();
            $order_common = array();
            $order_goods = array();

            /**
             * 新增逻辑，1、传咨询单号，order_type = 13，互联网医疗订单, 2、传推荐id的，属于电商普通订单 order_type=1，
             * consult_order_sn，recommend_id 存到同一个新增字段，方便互联网医疗那边查询
             */
            if (isset($_POST['consult_order_sn']) && $_POST['consult_order_sn']) { // 咨询单号
                $order['hospital_recommend_id'] = $_POST['consult_order_sn'];
                $order['order_type'] = 13;
            } else { // 推荐id
                $order['hospital_recommend_id'] = intval($_POST['recommend_id']);
                $order['order_type'] = 1;
            }
            $order['order_sn'] = $this->_logic_buy_1->makeOrderSn($order_pay_id);
            $order['pay_sn'] = $pay_sn;
            $order['store_id'] = $store_id;
            $order['store_name'] = $goods_list[0]['store_name'];
            $order['buyer_id'] = $member_id;
            $order['buyer_name'] = $member_name;
            $order['buyer_email'] = $member_email;
            $order['buyer_phone'] = $order_member_mobile; //is_numeric($reciver_phone) ? $reciver_phone : 0;
            $order['add_time'] = TIMESTAMP;
            $order['payment_code'] = $store_pay_type_list[$store_id];
            $order['order_state'] = $store_pay_type_list[$store_id] == 'offline' ? ORDER_STATE_PAY : ORDER_STATE_NEW;
            $order['order_amount'] = $store_final_order_total[$store_id];
            $order['shipping_fee'] = $store_freight_total[$store_id];
            $order['goods_amount'] = $order['order_amount'] - $order['shipping_fee'] + $store_rpt_total[$store_id];
            $power_state = true;
            if ($power_id && !$ifcart) {
                $power_info = $power_model->getPowerInfo(['power_id' => $power_id, 'power_status' => 1]);
                if ($power_info) {
                    //保存数据
                    $power_state = false;
                    $order['order_amount'] = $power_info['power_price'];
                    $order['shipping_fee'] = 0;
                    $order['goods_amount'] = $power_info['power_price'];
                    $order['order_type'] = 99;
                }
            }
            $order['order_from'] = $order_from;

            $order['chain_id'] = intval($input_chain_id ?: $this->_post_data['chain_id']);
            $order['rpt_amount'] = empty($store_rpt_total[$store_id]) ? 0 : $store_rpt_total[$store_id];
            $order['dis_type'] = $this->_post_data['dis_type'] ?: 5;
            $order['is_live'] = $this->_post_data['is_live'];
            $order['first_order'] = $this->_post_data['first_order'];

            //微信广告标识
            $order['click_id'] = $_SERVER['HTTP_CLICK_ID'] ?: 0;
            // 下单是否使用虚拟库存
            $order['is_use_virtual_stock'] = $goods_list[0]['is_open_virtual_stock'] ? 1 : 0;
            $order_id = $model_order->addOrder($order);
            if (!$order_id) {
                throw new Exception('订单保存失败[未生成订单数据]');
            }
            $order['order_id'] = $order_id;
            $order_list[$order_id] = $order;

            $order_common['order_id'] = $order_id;
            $order_common['store_id'] = $store_id;
            $order_common['order_message'] = isset($input_pay_message[$store_id]) ? $input_pay_message[$store_id] : '';
            $order_common['reciver_info'] = $reciver_info;
            $order_common['reciver_name'] = $reciver_name;
            $order_common['reciver_city_id'] = $input_city_id;
            $insert = $model_order->addOrderCommon($order_common);
            if (!$insert) {
                throw new Exception('订单保存失败[未生成订单扩展数据]');
            }

            //添加订单日志
            $log_data = array();
            $log_data['order_id'] = $order_id;
            $log_data['log_role'] = '买家';
            $log_data['log_msg'] = '生成订单';
            $log_data['log_user'] = $member_name;
            $log_data['log_orderstate'] = ORDER_STATE_NEW;
            $model_order->addOrderLog($log_data);

            //生成order_goods订单商品数据
            $i = 0;
            $order_is_dis = 0; //是否分销订单
            $order_dis_member_id = 0;
            foreach ($goods_list as $goods_info) {
                $goods_commonid = $goods_info['goods_commonid'];
                $order_goods[$i]['order_id'] = $order_id;
                $order_goods[$i]['goods_id'] = $goods_info['goods_id'];
                $order_goods[$i]['store_id'] = $store_id;
                $order_goods[$i]['goods_name'] = $goods_info['goods_name'];
                $order_goods[$i]['goods_price'] = $goods_info['goods_price'];
                $order_goods[$i]['goods_num'] = $goods_info['goods_num'];
                $order_goods[$i]['goods_image'] = $goods_info['goods_image'];
                $order_goods[$i]['goods_spec'] = $goods_info['goods_spec'];
                $order_goods[$i]['buyer_id'] = $member_id;
                $order_goods[$i]['goods_commonid'] = $goods_commonid;
                $order_goods[$i]['add_time'] = TIMESTAMP;
                $order_goods[$i]['sku'] = $goods_info['goods_sku'];
                $order_goods[$i]['chain_id'] = $this->_post_data['chain_id'] ? $this->_post_data['chain_id'] : 0; //增加门店id记录 lihaobin 2019-09-02


                if ($goods_info['is_member_price'] == 1) {
                    $order_goods[$i]['goods_type'] = 12;
                } elseif ($input_chain_id && !$goods_info['ifzengpin']) {
                    $order_goods[$i]['goods_type'] = 1;
                }
                $order_goods[$i]['promotions_id'] = $goods_info['promotions_id'] ? $goods_info['promotions_id'] : 0;
                $order_goods[$i]['commis_rate'] = 200;
                $order_goods[$i]['gc_id'] = $goods_info['gc_id'];
                //记录消费者保障服务
                $contract_itemid_arr = $goods_info['contractlist'] ? array_keys($goods_info['contractlist']) : array();
                $order_goods[$i]['goods_contractid'] = $contract_itemid_arr ? implode(',', $contract_itemid_arr) : '';
                //计算商品金额
                $goods_total = $goods_info['goods_price'] * $goods_info['goods_num'];
                //计算本件商品优惠金额
                $promotion_value = ceil($goods_total * ($promotion_rate) * 100) / 100; //优惠金额精确到1角
                $order_goods[$i]['goods_pay_price'] = $goods_total - $promotion_value < 0 ? 0 : $goods_total - $promotion_value;
                $promotion_sum += $promotion_value;
                $order_goods[$i]['is_dis'] = 0;
                $order_goods[$i]['dis_member_id'] = 0;
                $order_goods[$i]['dis_commis_rate'] = 0;

                $goods_common_info = $model_goods->getGoodsInfoByID($goods_info['goods_id'], 'is_dis,dis_commis_rate', $store_id);
                $goods_info['is_dis'] = $goods_common_info['is_dis'];
                $dis_member_id = $dis_member_info['dis_member_id'] ?: 0;
                $outside_member_id = $dis_member_info['outside_member_id'] ?: 0;

                $order_goods[$i]['is_live'] = $goods_info['is_live'] ? $goods_info['is_live'] : 0;
                $order_goods[$i]['is_group_goods'] = $goods_info['is_group_goods'];
                $i++;

                //存储库存报警数据
                if ($goods_info['goods_storage_alarm'] >= ($goods_info['goods_storage'] - $goods_info['goods_num'])) {
                    $param = array();
                    $param['common_id'] = $goods_info['goods_commonid'];
                    $param['sku_id'] = $goods_info['goods_id'];
                    $notice_list['goods_storage_alarm'][$goods_info['store_id']] = $param;
                }
            }
            $insert = $model_order->addOrderGoodsOne($order_goods);
            if (!empty($insert)) {
                $insert_id = $insert;
            }

            if (empty($insert)) {
                throw new Exception('订单保存失败[未生成商品数据01]');
            }
            $order_data = [];
            if ($order_is_dis) {
                //如果是自己扫自己的码购买
                if ($member_id == $dis_member_id) {
                    $order_data['dis_type'] = 3;
                }
                $order_data['is_dis'] = $order_is_dis;
                $order_data['chain_id'] = Logic('distribute')->getDisMemberChain($order_dis_member_id);
            } else {
                //如果有绑定关系，但是商品不是分销商品，那么也算门店订单 lihaobin 2020-02-10
                $order_data['is_dis'] = 0;
                $order_data['chain_id'] = Logic('distribute')->getDisMemberChain($dis_member_id);
            }
            $model_order->editOrder($order_data, array('order_id' => $order_id));

            //存储商家发货提醒数据
            if ($store_pay_type_list[$store_id] == 'offline') {
                $notice_list['new_order'][$order['store_id']] = array('order_sn' => $order['order_sn']);
            }

            $dc_goods_total = $order['goods_amount'] + $order_common['promotion_total'];
            $order_split['order']['old_order_sn'] = $order['order_sn']; //订单编号
            $order_split['order']['buyer_memo'] = $order_common['order_message']; //买家留言
            $order_split['order']['channel_id'] = $order['order_type'] == 13 ? 7 : 5; //渠道ID：1阿闻到家,2美团,3饿了么,4京东到家,5阿闻电商,6门店,7互联网医疗
            $order_split['order']['expected_time'] = ""; //预计送达时间
            $order_split['order']['freight'] = $order['shipping_fee'] ? $this->intval_format_amount($order['shipping_fee'] * 100) : 0; //总运费
            $order_split['order']['goods_total'] = $order['goods_amount'] ? $this->intval_format_amount($dc_goods_total * 100) : 0; //商品总金额
            $order_split['order']['invoice'] = "";
            $order_split['order']['is_split'] = 0; //是否有拆单，0否1是
            $order_split['order']['is_virtual'] = 0; //是否是虚拟订单，0否1是
            $order_split['order']['latitude'] = 0; //收货地址纬度
            $order_split['order']['longitude'] = 0; //收货地址经度
            $order_split['order']['order_type'] = $order['order_type']; //订单类型1普通订单(默认),2预定订单,3门店自提(电商订单专用),4拼团订单,5门店配送,6健康计划,7保险订单,8积分订单,9周期购 99助力
            $order_split['order']['privilege'] = $order_common['promotion_total'] ? $this->intval_format_amount($order_common['promotion_total'] * 100) : 0; //总优惠金额
            $datacenter_address_info = unserialize($order_common['reciver_info']);
            $datacenter_area_info_arr = explode(" ", $datacenter_address_info['area']);
            $order_split['order']['receiver_address'] = $datacenter_address_info['address']; //收件地址
            $order_split['order']['receiver_state'] = $datacenter_area_info_arr[0] ? $datacenter_area_info_arr[0] : ""; //收件省
            $order_split['order']['receiver_city'] = $datacenter_area_info_arr[1] ? $datacenter_area_info_arr[1] : ""; //收件市
            $order_split['order']['receiver_district'] = $datacenter_area_info_arr[2] ? $datacenter_area_info_arr[2] : ""; //收件区
            $order_split['order']['receiver_name'] = $order_common['reciver_name']; //收件人
            $order_split['order']['receiver_phone'] = $reciver_phone; //收件电话
            $order_split['order']['shop_id'] = (string)$order['store_id']; //商户或门店id
            $order_split['order']['shop_name'] = $order['store_name']; //商户名称
            $order_split['order']['total'] = $order['order_amount'] ? $this->intval_format_amount($order['order_amount'] * 100) : 0; //总金额（付款金额，加上运费，减优惠金额）
            $order_split['order']['user_agent'] = order_source($_POST['source']); //渠道来源,1-Android,2-iOS,3-小程序,4-公众号,5-Web,6-其它
            $order_split['order']['power_id'] = $power_id; //助力订单

            $order_split['order']['order_pay_type'] = $order_data['is_dis'] ?
                OrderAlias::ORDER_PAY_TYPE_DIS : OrderAlias::ORDER_PAY_TYPE_NOT_DIS;

            $order_split['order_products'] = $order_goods;
            foreach ($order_split['order_products'] as $k => $v) {
                $goods_info_type = $model_goods->getGoodsInfo(array("goods_id" => $v['goods_id']));
                $goods_sku_type = intval($goods_info_type['goods_sku_type']);
                $order_split['order_products'][$k]['child_product_list'] = [];
                $order_split['order_products'][$k]['discount_count'] = 0; // 参与限时折扣的商品数量
                $order_split['order_products'][$k]['discount_price'] = $this->intval_format_amount($v['goods_pay_price'] * 100); // 折扣价格
                $order_split['order_products'][$k]['image'] = isset($v['goods_image']) ? (string)$v['goods_image'] : ""; // 商品图片
                //$order_split['order_products'][$k]['is_have_reality'] = $v['is_group_goods'] > 0 ? 1 : 0;// 针对组合商品，是否有实物商品（0-没有1-有）
                $order_split['order_products'][$k]['specs'] = isset($v['goods_spec']) ? (string)$v['goods_spec'] : "";
                $order_split['order_products'][$k]['number'] = intval($v['goods_num']); // 数量
                $order_split['order_products'][$k]['parent_sku'] = ""; // 组合商品父级sku
                $order_split['order_products'][$k]['price'] = $this->intval_format_amount($v['goods_price'] * 100); // 单价
                $order_split['order_products'][$k]['product_id'] = isset($v['goods_id']) ? (string)$v['goods_id'] : ""; //(string)$v['goods_id'];// 商品id
                $order_split['order_products'][$k]['product_name'] = (string)$v['goods_name']; // 商品名称
                $order_split['order_products'][$k]['product_type'] = $v['is_group_goods'] > 0 ? 3 : 1; // 商品类型1-实物商品，2-虚拟商品，3-组合商品
                $order_split['order_products'][$k]['promotion_id'] = isset($v['promotions_id']) ? intval($v['promotions_id']) : 0; // 促销活动Id
                $order_split['order_products'][$k]['promotion_type'] = 0; // 活动类型1-满减商品2限时折扣3-满减运费
                $order_split['order_goods'][$k]['source'] = $goods_sku_type ? $goods_sku_type : 1;
                $order_split['order_products'][$k]['bar_code'] = $goods_info_type['goods_barcode'] ? $goods_info_type['goods_barcode'] : "";
                $order_split['order_products'][$k]['sku'] = isset($v['goods_id']) ? (string)$v['goods_id'] : "";
                $order_split['order_products'][$k]['rec_id'] = $insert_id[$k] ? $insert_id[$k] : 0;
                $order_split['order_products'][$k]['combine_type'] = intval($this->getCombineType($v['is_group_goods'])); //组合商品组合类型0-非组合31-实物实物32-实物虚拟33-虚拟虚拟
                $order_split['order_products'][$k]['term_type'] = 0;
                $order_split['order_products'][$k]['term_value'] = 0;
                $order_split['order_products'][$k]['warehouse_type'] = (int)$goods_info_type['warehouse_type'];
            }
            $order_split['order_promotions'] = [];
            $order_split['pay_info'] = (object)[];

            unset($order_split['order_goods']);

            if (!$this->_post_data['ignore_push']) {
                //推送订单到数据中台
                $rs =  $this->push_datacenter_order($order_split, $order['buyer_phone']);
                if ($rs['order_sn']) {
                    $model_order->editOrder([
                        'erp_order_id' => $rs['order_sn']
                    ], array('order_id' => $order_id));
                }
            }
        }

        //保存数据
        $this->_order_data['pay_sn'] = $pay_sn;
        $this->_order_data['order_sn'] = $order['order_sn'];
        $this->_order_data['order_list'] = $order_list;
        $this->_order_data['notice_list'] = $notice_list;
        $this->_order_data['ifgroupbuy'] = $ifgroupbuy;
        $this->_order_data['ifbook'] = $goods_list[0]['is_book'] == 1;
        $this->_order_data['order_id'] = $order_id;
    }

    /**
     * 生成订单 拼团用
     * @param array $input
     * @throws Exception
     * @return array array(支付单sn,订单列表)
     */
    private function  _createOrderStep11()
    {

        extract($this->_order_data);
        $ifcart = $this->_post_data['ifcart'];
        $order_member_mobile = $this->_member_info['member_mobile'];
        $model_goods = Model('goods');
        //存储生成的订单数据
        $order_list = array();
        //支付方式
        if ($input_pay_name == 'chain' && $input_chain_id) {
            $store_pay_type_list = array(key($store_cart_list) => 'chain');
        } else {
            //每个店铺订单是货到付款还是线上支付,店铺ID=>付款方式[在线支付/货到付款]
            $store_pay_type_list = $this->_logic_buy_1->getStorePayTypeList(array_keys($store_cart_list), $input_if_offpay, $input_pay_name);
            foreach ($store_pay_type_list as $k => $v) {
                if (empty($input_if_offpay_batch[$k]))
                    $store_pay_type_list[$k] = 'online';
            }
        }

        foreach ($store_cart_list as $store_id => $goods_list) {
            //取得本店优惠额度(后面用来计算每件商品实际支付金额，结算需要)
            $promotion_total = !empty($store_promotion_total[$store_id]) ? $store_promotion_total[$store_id] : 0;

            //本店总的优惠比例,保留3位小数
            // $should_goods_total = $store_final_order_total[$store_id]-$store_freight_total[$store_id]+$promotion_total;
            $should_goods_total = $store_goods_total[$store_id];
            $promotion_rate = abs(number_format($promotion_total / $should_goods_total, 5));
            if ($promotion_rate <= 1) {
                $promotion_rate = floatval(substr($promotion_rate, 0, 5));
            } else {
                $promotion_rate = 0;
            }
            //每种商品的优惠金额累加保存入 $promotion_sum
            $promotion_sum = 0;

            $order = array();
            $product_list = array();

            $order_amount = ncPriceFormat($store_final_order_total[$store_id]);
            $pintuan_order = array();
            $business_json = array();
            $business_json['is_live']                   = $this->_post_data['is_live'];
            $business_json['first_order']               = $this->_post_data['first_order'];
            $business_json['dis_type']                  = $this->_post_data['dis_type'] ? $this->_post_data['dis_type'] : 5;
            $business_json['chain_id']                  = $input_chain_id ?: $this->_post_data['chain_id'];
            $business_json['order_from']                = $order_from;
            $business_json['buyer_msg']                 = isset($input_pay_message[$store_id]) ? $input_pay_message[$store_id] : '';
            $business_json['buyer_phone']               = $order_member_mobile;
            $business_json['address_id']                = $this->_post_data['address_id'];
            $business_json['address_info']              = $this->_post_data['input_address_info'];
            $business_json['cart_id']                   = $this->_post_data['pt_cart_id'];
            $business_json['gid']                       = $this->_post_data['gid'];
            $business_json['pin_type']                  = $this->_post_data['pin_type'];
            $business_json['voucher']                   = $input_voucher_list;
            $business_json['dis_id']                    = $this->_post_data['pt_dis_id'];
            $business_json['pay_message']               = $this->_post_data['pt_pay_message'];
            $business_json['power_state']               = $this->_post_data['power_state'];
            $business_json['pay_name']                  = $this->_post_data['pay_name'];
            $business_json['source']                    = $this->_post_data['source'];
            $business_json['store_freight_total']       = intval($store_freight_total[$store_id] * 100);

            $business_json_str                          = json_encode($business_json, JSON_UNESCAPED_UNICODE);
            $pintuan_order['business_json']             = addslashes($business_json_str); // 业务端json string
            $pintuan_order['channel_id']                = 5; // 渠道id integer
            $pintuan_order['is_virtual']                = 0; // 是否虚拟订单 0否 1是 integer
            $pintuan_order['end_time']                  = $goods_list[0]['pintuan_info']['end_date']; // 结束时间 string
            $pintuan_order['group_buy_id']              = intval($goods_list[0]['pintuan_info']['gid']); // 拼团活动id integer
            $pintuan_order['max_participant_number']    = intval($goods_list[0]['pintuan_info']['success_num']); // 最大参与人数 integer
            $pintuan_order['open_id']                   = $this->_post_data['weixin_mini_openid']; // 小程序的用户id string
            $pintuan_order['open_name']                 = $this->_post_data['wx_name']; // 用户昵称 string
            $pintuan_order['is_pin_head']               = strlen($this->_post_data['pin_head_order_sn']) > 10 ? 0 : 1; //  是否团长 1是 0 否 integer
            $pintuan_order['pay_price']                 = $order_amount * 100; // 支付金额(分) integer
            $pintuan_order['pin_head_order_sn']         = strlen($this->_post_data['pin_head_order_sn']) > 10 ? $this->_post_data['pin_head_order_sn'] : ""; // 参团需要带上团长订单号 string
            $pintuan_order['portrait']                  = $this->_post_data['wx_img']; // 头像 string
            $pintuan_order['shop_id']                   = "1"; // 店铺财务编码 string
            $pintuan_order['sku_id']                    = (string)$goods_list[0]['goods_id']; // 拼团商品sku_id string
            $pintuan_order['start_time']                = $goods_list[0]['pintuan_info']['begin_date']; // 开始时间 string
            $pintuan_order['status']                    = 10; // 订单状态：0已取消 10未支付 20拼团进行中 30拼团成功 40拼团失败 integer
            $pintuan_order['submit_json']               = ""; // 提交到订单中心json string 暂时不用了
            $pintuan_order['total_price']               = $order_amount * 100; // 总金额 integer
            $pintuan_order['user_id']                   = $this->_post_data['scrm_user_id']; // 用户id string
            $pintuan_order['order_pay_type'] = $business_json['dis_id'] ?
                OrderAlias::ORDER_PAY_TYPE_DIS : OrderAlias::ORDER_PAY_TYPE_NOT_DIS;

            $i = 0;
            foreach ($goods_list as $goods_info) {
                $product_list[$i]['marking_price']  = intval($goods_info['goods_old_price'] * 100); // 商品原单价 integer
                $product_list[$i]['max_number']     = intval($goods_info['pintuan_info']['buy_limit_num']); // 单人购买的最大数量 integer
                $product_list[$i]['number']         = intval($goods_info['goods_num']); // 数量 integer
                $product_list[$i]['pay_price']      = $goods_info['pintuan_info']['price']; // 商品均摊后实际支付单价 integer
                $product_list[$i]['pin_price']      = $goods_info['pintuan_info']['price']; // 商品拼团单价 integer
                $product_list[$i]['product_image']  = $goods_info['goods_image']; // 商品图片 string
                $product_list[$i]['product_name']   = $goods_info['goods_name']; // 商品名称 string
                $product_list[$i]['product_type']   = 1; // 商品类别（1-实物商品，2-虚拟商品） integer
                $product_list[$i]['sku_id']         = (string)$goods_info['goods_id']; // 商品SKUID string
                $product_list[$i]['spec_name']      = $goods_info['goods_spec']; // 规格值 string
                if ($goods_info['is_group_goods'] > 0) { //组合商品
                    $child_goods_list = GoodsGroup::getGroupGoodsList(array('goods_type' => $goods_info['is_group_goods'], 'goods_commonid' => $goods_info['goods_commonid'])); //$goods_info['group_goods_list'];
                    if (is_array($child_goods_list) && !empty($child_goods_list)) {
                        foreach ($child_goods_list as $kk => $vv) {
                            $vv_goods_info = $model_goods->getGoodsInfo(array("goods_commonid" => $vv['goods_commonid']), "goods_id,goods_sku_type,goods_barcode,goods_image,goods_spec,goods_sku");
                            $product_list[$i]['childProduct'][$kk]['marking_price'] = $this->intval_format_amount($vv['goods_price'] * 100);
                            $product_list[$i]['childProduct'][$kk]['max_number'] = 1;
                            $product_list[$i]['childProduct'][$kk]['number'] = intval($vv['goods_number']);
                            $product_list[$i]['childProduct'][$kk]['pay_price'] = $this->intval_format_amount($vv['goods_price'] * 100);
                            $product_list[$i]['childProduct'][$kk]['pin_price'] = $this->intval_format_amount($vv['goods_price'] * 100);
                            $product_list[$i]['childProduct'][$kk]['product_image'] = isset($vv['goods_image_url']) ? (string)$vv['goods_image_url'] : "";
                            $product_list[$i]['childProduct'][$kk]['product_name'] = (string)$vv['goods_name'];
                            $product_list[$i]['childProduct'][$kk]['product_type'] = 1;
                            $product_list[$i]['childProduct'][$kk]['sku_id'] = isset($vv_goods_info['goods_id']) ? (string)$vv_goods_info['goods_id'] : "";
                            $goods_spec = unserialize($vv_goods_info['goods_spec']);
                            $product_list[$i]['childProduct'][$kk]['spec_name'] = isset($goods_spec[1]) ? (string)$goods_spec[1] : "";
                        }
                    }
                    $product_list[$i]['product_type']  = 3;
                } else {
                    $order_split[$i]['childProduct'] = [];
                }
                $i++;
            }
            $pintuan_order['product_list'] = $product_list; // 拼团商品信息

            //更新使用的代金券状态 添加优惠券记录
            if (!empty($input_voucher_list) && !empty($business_json['voucher'] && $business_json['voucher'] != 'null' && $this->_post_data['pintuan'] != 2)) {
                $result = Logic('realtime_msg')->editVoucherState($input_voucher_list, $input_chain_id);
                if (!$result['state']) {
                    throw new Exception('订单保存失败[代金券处理失败]');
                }
            }

            $jsonstr = json_encode($pintuan_order, JSON_UNESCAPED_UNICODE);
            list($code, $response) = http_post_json(MALL_CENTER_ADDR . "/mall/pin-order/create", $jsonstr, "", "", 4, "5", $this->_post_data['source']);
            $order_result = json_decode($response, true);
            if ($code != 200 || $order_result['code'] != 200) {
                if (!empty($order_result['message'])) {
                    throw new Exception($order_result['message']);
                }
                throw new Exception("推送订单失败！");
            }
            $order['order_sn'] = $order_result['pin_order_sn'];
            $order_list[$order_result['pin_order_sn']] = $order;
            //添加优惠券记录
            $model_goods->addPinStocklog([
                'ps_order_sn' => $order_result['pin_order_sn'],
                //                'voucher_ids' => $business_json['voucher'],
                'voucher_ids' => $input_voucher_list ? json_encode($input_voucher_list) : '',
                'member_id' => $this->_member_info['member_id'],
                'ps_addtime' => time()
            ]);
        }
        $this->_order_data['order_sn'] = $order['order_sn'];
        $this->_order_data['order_list'] = $order_list;
    }

    //得到数据中心所需商品类型
    public function getCombineType($type)
    {
        $CombineType = array(0 => '0', 1 => '31', 2 => '33', 3 => '32'); //'0非组合，1实实组合，2虚虚组合，3虚实组合'
        return $CombineType[$type];
    }

    //格式化金额 乘以倍数转int 总金额为8分钱以下需要转
    public function intval_format_amount($amount)
    {
        return intval(sprintf("%.2f", ($amount)));
    }

    /**
     * @param $order_data 订单信息
     */
    public function push_datacenter_order($order_data, $phone, $type = 0)
    {
        // 新增互联网医疗订单推送 order_type = 1 标记普通订单(原值为13)，channel_id = 7 标记互联网订单
        $channel_id = '5';
        if ($order_data['order']['order_type'] == 13) {
            $order_data['order']['order_type'] = 1;
            $order_data['order']['channel_id'] = 7;
            $channel_id = '7';
        }

        $jsonstr = json_encode($order_data, JSON_UNESCAPED_UNICODE);
        list($code, $response) = http_post_json(
            C('datacenter_orderpay_url') . "/order-api/order/docommit",
            $jsonstr,
            $phone,
            $order_data['order']['old_order_sn'],
            $type,
            $channel_id,
            $order_data['order']['user_agent']
        );
        $order_arr = json_decode($response, true);

        $log = [
            //记录当前方法和行数
            'file' => __FILE__ . ':' . __LINE__ . ':' . __FUNCTION__,
            'params' => ['order_sn' => $order_data['order']['old_order_sn'], 'receiver_phone' => hideStr($order_data['order']['receiver_phone'])],
            'res' => $order_arr
        ];

        if ($code != '200' || $order_arr['code'] != '200') {
            log_error('订单推送失败', $log);
            //无库存商品要返回给前端弹窗
            if (isset($order_arr['cannot_products']) && $order_arr['cannot_products']) {
                $goods_arr = [];
                foreach ($order_arr['cannot_products'] as $v) {
                    $info = Model('goods')->getGoodsInfoById($v['sku_id']);
                    if ($info) {
                        $info['state'] = false;
                        $info['storage_state'] = false;
                        $info['goods_num'] = 0;
                        $info['goods_image_url'] = $info['goods_image'];
                        $info['goods_id'] = $v['sku_id'];
                        $info['invalid_type']  = $v['status']; // 1:无货 2:失效 3:下架

                        $goods_arr[] = $info;
                    }
                }
                log_error('订单推送失败-库存不足', $goods_arr);
                throw (new SubmitStockException('库存不足'))->setStockData(['is_stock' => true, 'datas' => [1 => $goods_arr]]);
            }

            if (!empty($order_arr['message'])) {
                throw new Exception($order_arr['message']);
            }
            throw new Exception("推送订单失败！");
        }
        return $order_arr;
    }

    /**
     * 订单后续其它处理
     *
     */
    private function _createOrderStep6()
    {
        $ifcart = $this->_post_data['ifcart'];
        $goods_buy_quantity = $this->_order_data['goods_buy_quantity'];
        $input_voucher_list = $this->_order_data['input_voucher_list'];
        $input_buy_items = $this->_order_data['input_buy_items'];
        $order_list = $this->_order_data['order_list'];
        $input_chain_id = $this->_order_data['input_chain_id'];
        $goods_sale = $this->_order_data['goods_sale'];

        //变更库存和销量
        foreach ($goods_buy_quantity as $goods_id => $quantity) {
            $push_data = [
                'goods_id' => $goods_id,
                'goods_commonid' => $goods_sale[$goods_id],
                'quantity' => $quantity,
                'store_id' => $_REQUEST['store_id'],
            ];
            SyncUpdateGoodsStorageQueue::dispatch($push_data);
        }
        //更新使用的代金券状态
        if (!empty($input_voucher_list) && is_array($input_voucher_list) && $this->_post_data['pintuan'] != 2) {
            $result = Logic('realtime_msg')->editVoucherState($input_voucher_list, $input_chain_id);
            if (!$result['state']) {
                throw new Exception('订单保存失败[代金券处理失败]');
            }
        }
        //删除购物车中的商品
        $this->delCart($ifcart, $this->_member_info['member_id'], array_keys($input_buy_items));
        @setNcCookie('cart_goods_num', '', -3600);

        //生成交易快照
        $order_id_list = array();
        foreach ($order_list as $order_info) {
            $order_id_list[] = $order_info['order_id'];
        }
        QueueClient::push('createSphot', $order_id_list);

        //异步更新无保护分销关系失败状态
        if (intval(C('distri_user_time')) == 0) {
            SyncDisMemberFansQueue::dispatch($this->_member_info['member_id']);
        }

        //异步更新福码购分销客户订单数据
        $dis_member_info = $this->_order_data['dis_member_info'];
        if ($_POST['store_id'] == 3 && $dis_member_info['dis_id'] > 0 && !empty($dis_member_info['member_id']) && $dis_member_info['order_amount']  > 0) {
            SyncDisDistributorFansOrder::dispatch($dis_member_info);
        }
    }

    /**
     * 加密
     * @param array/string $string
     * @param int $member_id
     * @return mixed arrray/string
     */
    public function buyEncrypt($string, $member_id)
    {
        $buy_key = sha1(md5($member_id . '&' . MD5_KEY));
        if (is_array($string)) {
            $string = serialize($string);
        } else {
            $string = strval($string);
        }
        return encrypt(base64_encode($string), $buy_key);
    }

    /**
     * 解密
     * @param string $string
     * @param int $member_id
     * @param number $ttl
     */
    public function buyDecrypt($string, $member_id, $ttl = 0)
    {
        $buy_key = sha1(md5($member_id . '&' . MD5_KEY));
        if (empty($string)) return;
        $string = base64_decode(decrypt(strval($string), $buy_key, $ttl));
        return ($tmp = @unserialize($string)) !== false ? $tmp : $string;
    }

    /**
     * 得到所购买的id和数量
     *
     */
    public function _parseItems($cart_id)
    {

        //存放所购商品ID和数量组成的键值对
        $buy_items = array();
        if (is_array($cart_id)) {
            foreach ($cart_id as $value) {
                //转义特殊字符
                $value = urldecode($value);
                if (preg_match_all('/^(\d{1,10})\|(\d{1,6})$/', $value, $match)) {
                    if (intval($match[2][0]) > 0) {
                        $buy_items[$match[1][0]] = $match[2][0];
                    }
                }
            }
        }
        return $buy_items;
    }

    /**
     * 从购物车数组中得到商品列表
     * @param unknown $cart_list
     */
    private function _getGoodsList($cart_list)
    {
        if (empty($cart_list) || !is_array($cart_list)) return $cart_list;
        $goods_list = array();
        $i = 0;
        foreach ($cart_list as $key => $cart) {
            if (!$cart['state'] || !$cart['storage_state']) continue;
            //购买数量
            $quantity = $cart['goods_num'];
            if (!intval($cart['bl_id'])) {
                //如果是普通商品
                $goods_list[$i]['goods_num'] = $quantity;
                $goods_list[$i]['goods_id'] = $cart['goods_id'];
                $goods_list[$i]['store_id'] = $cart['store_id'];
                $goods_list[$i]['gc_id'] = $cart['gc_id'];
                $goods_list[$i]['goods_name'] = $cart['goods_name'];
                $goods_list[$i]['goods_price'] = $cart['goods_price'];
                $goods_list[$i]['store_name'] = $cart['store_name'];
                $goods_list[$i]['goods_image'] = $cart['goods_image'];
                $goods_list[$i]['transport_id'] = $cart['transport_id'];
                $goods_list[$i]['goods_freight'] = $cart['goods_freight'];
                $goods_list[$i]['goods_trans_v'] = $cart['goods_trans_v'];
                $goods_list[$i]['goods_inv'] = $cart['goods_inv'];
                $goods_list[$i]['goods_vat'] = $cart['goods_vat'];
                $goods_list[$i]['goods_voucher'] = $cart['goods_voucher'];
                $goods_list[$i]['is_fcode'] = $cart['is_fcode'];
                $goods_list[$i]['bl_id'] = 0;
                $goods_list[$i]['chain_id'] = $cart['chain_id']; //门店id
                $goods_list[$i]['freight'] = $cart['freight']; //是否包邮
                $goods_list[$i]['is_live'] = $cart['is_live']; //是否直播
                $goods_list[$i]['goods_sku'] = $cart['goods_sku'];
                $i++;
            } else {
                //如果是优惠套装商品
                foreach ($cart['bl_goods_list'] as $bl_goods) {
                    $goods_list[$i]['goods_num'] = $quantity;
                    $goods_list[$i]['goods_id'] = $bl_goods['goods_id'];
                    $goods_list[$i]['store_id'] = $cart['store_id'];
                    $goods_list[$i]['gc_id'] = $bl_goods['gc_id'];
                    $goods_list[$i]['goods_name'] = $bl_goods['goods_name'];
                    $goods_list[$i]['goods_price'] = $bl_goods['goods_price'];
                    $goods_list[$i]['store_name'] = $bl_goods['store_name'];
                    $goods_list[$i]['goods_image'] = $bl_goods['goods_image'];
                    $goods_list[$i]['transport_id'] = $bl_goods['transport_id'];
                    $goods_list[$i]['goods_freight'] = $bl_goods['goods_freight'];
                    $goods_list[$i]['goods_inv'] = $bl_goods['goods_inv'];
                    $goods_list[$i]['goods_vat'] = $bl_goods['goods_vat'];
                    $goods_list[$i]['goods_voucher'] = $bl_goods['goods_voucher'];
                    $goods_list[$i]['bl_id'] = $cart['bl_id'];
                    $goods_list[$i]['chain_id'] = $cart['chain_id']; //门店id
                    $goods_list[$i]['freight'] = $cart['freight']; //是否包邮
                    $goods_list[$i]['is_live'] = $cart['is_live']; //是否直播
                    $goods_list[$i]['goods_sku'] = $cart['goods_sku'];
                    $i++;
                }
            }
        }
        return $goods_list;
    }

    /**
     * 将下单商品列表转换为以店铺ID为下标的数组
     *
     * @param array $cart_list
     * @return array
     */
    private function _getStoreCartList($cart_list)
    {
        if (empty($cart_list) || !is_array($cart_list)) return $cart_list;
        $new_array = array();
        foreach ($cart_list as $cart) {
            $new_array[$cart['store_id']][] = $cart;
        }
        return $new_array;
    }

    /**
     * 本次下单是否需要码及F码合法性
     * 无需使用F码，返回 true
     * 需要使用F码，返回($fc_id/false)
     */
    private function _checkFcode($goods_list, $fcode)
    {
        foreach ($goods_list as $k => $v) {
            if ($v['is_fcode'] == 1) {
                $is_fcode = true;
                break;
            }
        }
        if (!$is_fcode) return true;
        if (empty($fcode) || count($goods_list) > 1) {
            return false;
        }
        $goods_info = $goods_list[0];
        $fcode_info = $this->checkFcode($goods_info['goods_id'], $fcode);
        if ($fcode_info['state']) {
            return intval($fcode_info['data']['fc_id']);
        } else {
            return false;
        }
    }

    /**
     * 验证商品是否支持自提
     * @param unknown $goods_list
     * @return boolean
     */
    private function _checkChain($goods_list)
    {
        if (empty($goods_list) || !is_array($goods_list)) return false;
        $_flag = true;
        foreach ($goods_list as $goods_info) {
            if (!$goods_info['is_chain']) {
                $_flag = false;
                break;
            }
        }
        return $_flag;
    }

    /**
     * 同步拆单订单信息
     */
    public function syncDemolitionOrderInfo($order_id)
    {
        $model_order = Model('order');
        $model_goods = Model('goods');
        $model_order_demolition = Model('order_demolition');
        //$model_member = Model('member');
        $orderInfo = $model_order->getOrderInfo(['order_id' => $order_id], [], "order_id,is_live,dis_type,order_demolition,order_state,payment_code,payment_time,trade_no,payment_from", '', '', true);
        if (is_array($orderInfo) && !empty($orderInfo)) {
            $demolition_order_list = $model_order_demolition->getOrderList(['order_id' => $order_id]);
            if (is_array($demolition_order_list) && !empty($demolition_order_list)) {
                foreach ($demolition_order_list as $key => $demolition_order) {
                    $order_common = array();
                    $order_data = array();
                    $order_goods = array();
                    $order_data['dis_type'] = $orderInfo['dis_type'];
                    $order_data['is_live'] = $orderInfo['is_live'];
                    $order_data['order_sn'] = $demolition_order['order_sn'];
                    $order_data['pay_sn'] = $demolition_order['pay_sn'];
                    $order_data['pay_snold'] = $demolition_order['pay_snold'];
                    $order_data['pay_sn1'] = $demolition_order['pay_sn1'];
                    $order_data['store_id'] = $demolition_order['store_id'];
                    $order_data['store_name'] = $demolition_order['store_name'];
                    $order_data['buyer_id'] = $demolition_order['buyer_id'];
                    $order_data['buyer_name'] = $demolition_order['buyer_name'];
                    $order_data['buyer_email'] = $demolition_order['buyer_email'];
                    $order_data['buyer_phone'] = $demolition_order['buyer_phone'];
                    $order_data['add_time'] = $demolition_order['add_time'];
                    $order_data['payment_code'] = $orderInfo['payment_code'];
                    $order_data['payment_time'] = $orderInfo['payment_time'];
                    $order_data['finnshed_time'] = $demolition_order['finnshed_time'];
                    $order_data['goods_amount'] = $demolition_order['goods_amount'];
                    $order_data['order_amount'] = $demolition_order['order_amount'];
                    $order_data['rcb_amount'] = $demolition_order['rcb_amount'];
                    $order_data['pd_amount'] = $demolition_order['pd_amount'];
                    $order_data['shipping_fee'] = $demolition_order['shipping_fee'];
                    $order_data['evaluation_state'] = $demolition_order['evaluation_state'];
                    $order_data['evaluation_again_state'] = $demolition_order['evaluation_again_state'];
                    $order_data['order_state'] = $orderInfo['order_state'];
                    $order_data['refund_state'] = $demolition_order['refund_state'];
                    $order_data['lock_state'] = $demolition_order['lock_state'];
                    $order_data['delete_state'] = $demolition_order['delete_state'];
                    $order_data['refund_amount'] = $demolition_order['refund_amount'];
                    $order_data['delay_time'] = $demolition_order['delay_time'];
                    $order_data['order_from'] = $demolition_order['order_from'];
                    $order_data['shipping_code'] = $demolition_order['shipping_code'];
                    $order_data['order_type'] = $demolition_order['order_type'];
                    $order_data['api_pay_time'] = $demolition_order['api_pay_time'];
                    $order_data['chain_id'] = $demolition_order['chain_id'];
                    $order_data['chain_code'] = $demolition_order['chain_code'];
                    $order_data['rpt_amount'] = $demolition_order['rpt_amount'];
                    $order_data['trade_no'] = $orderInfo['trade_no'];
                    $order_data['is_dis'] = $demolition_order['is_dis'];
                    $order_data['chain_sender_state'] = $demolition_order['chain_sender_state'];
                    $order_data['erp_time'] = $demolition_order['erp_time'];
                    $order_data['erp_status'] = $demolition_order['erp_status'];
                    $order_data['erp_order_status'] = $demolition_order['erp_order_status'];
                    $order_data['erp_order_id'] = $demolition_order['erp_order_id'];
                    $order_data['erp_mobile'] = $demolition_order['erp_mobile'];
                    $order_data['billcode'] = $demolition_order['billcode'];
                    $order_data['gjp_time'] = $demolition_order['gjp_time'];
                    $order_data['order_father'] = $order_id;
                    $order_data['d_order_id'] = $demolition_order['d_order_id'];
                    $order_data['demolition_from'] = $demolition_order['source']; //拆单来源
                    $order_data['payment_from'] = $orderInfo['payment_from']; //支付来源
                    $new_order_id = $model_order->addOrder($order_data);
                    if (!$new_order_id) {
                        throw new Exception('订单保存失败[未生成订单数据02]');
                    }
                    $demolition_common_orderinfo = $model_order_demolition->getOrderCommonInfo(['d_order_id' => $demolition_order['d_order_id']]);
                    $order_common['order_id'] = $new_order_id;
                    $order_common['store_id'] = $demolition_common_orderinfo['store_id'];
                    $order_common['shipping_time'] = $demolition_common_orderinfo['shipping_time'];
                    $order_common['shipping_express_id'] = $demolition_common_orderinfo['shipping_express_id'];
                    $order_common['evaluation_time'] = $demolition_common_orderinfo['evaluation_time'];
                    $order_common['order_message'] = $demolition_common_orderinfo['order_message'];
                    $order_common['order_pointscount'] = $demolition_common_orderinfo['order_pointscount'];
                    $order_common['voucher_price'] = $demolition_common_orderinfo['voucher_price'];
                    $order_common['voucher_code'] = $demolition_common_orderinfo['voucher_code'];
                    $order_common['deliver_explain'] = $demolition_common_orderinfo['deliver_explain'];
                    $order_common['daddress_id'] = $demolition_common_orderinfo['daddress_id'];
                    $order_common['reciver_name'] = $demolition_common_orderinfo['reciver_name'];
                    $order_common['reciver_info'] = $demolition_common_orderinfo['reciver_info'];
                    $order_common['reciver_province_id'] = $demolition_common_orderinfo['reciver_province_id'];
                    $order_common['reciver_city_id'] = $demolition_common_orderinfo['reciver_city_id'];
                    $order_common['invoice_info'] = $demolition_common_orderinfo['invoice_info'];
                    $order_common['promotion_info'] = $demolition_common_orderinfo['promotion_info'];
                    $order_common['dlyo_pickup_code'] = $demolition_common_orderinfo['dlyo_pickup_code'];
                    $order_common['promotion_total'] = $demolition_common_orderinfo['promotion_total'];
                    $order_common['discount'] = $demolition_common_orderinfo['discount'];
                    $order_common['reciver_date_msg'] = $demolition_common_orderinfo['reciver_date_msg'];
                    $insert = $model_order->addOrderCommon($order_common);
                    if (!$insert) {
                        throw new Exception('订单保存失败[未生成订单扩展数据]');
                    }
                    $demolition_order_goods_list = $model_order_demolition->getOrderGoodsList(['d_order_id' => $demolition_order['d_order_id']]);
                    $i = 0;
                    if (is_array($demolition_order_goods_list) && !empty($demolition_order_goods_list)) {
                        foreach ($demolition_order_goods_list as $kk => $vv) {
                            $order_goods[$i]['order_id'] = $new_order_id;
                            $order_goods[$i]['goods_id'] = $vv['goods_id'];
                            $order_goods[$i]['goods_name'] = $vv['goods_name'];
                            $order_goods[$i]['goods_price'] = $vv['goods_price'];
                            $order_goods[$i]['goods_num'] = $vv['goods_num'];
                            $order_goods[$i]['goods_image'] = $vv['goods_image'];
                            $order_goods[$i]['goods_pay_price'] = $vv['goods_pay_price'];
                            $order_goods[$i]['store_id'] = $vv['store_id'];
                            $order_goods[$i]['buyer_id'] = $vv['buyer_id'];
                            $order_goods[$i]['goods_type'] = $vv['goods_type'];
                            $order_goods[$i]['promotions_id'] = $vv['promotions_id'];
                            $order_goods[$i]['commis_rate'] = $vv['commis_rate'];
                            $order_goods[$i]['gc_id'] = $vv['gc_id'];
                            $order_goods[$i]['goods_spec'] = $vv['goods_spec'];
                            $order_goods[$i]['goods_contractid'] = $vv['goods_contractid'];
                            $order_goods[$i]['goods_commonid'] = $vv['goods_commonid'];
                            $order_goods[$i]['add_time'] = $vv['add_time'];
                            $order_goods[$i]['is_dis'] = $vv['is_dis'];
                            $order_goods[$i]['dis_commis_rate'] = $vv['dis_commis_rate'];
                            $order_goods[$i]['dis_member_id'] = $vv['dis_member_id'];
                            $order_goods[$i]['out_member_id'] = $vv['out_member_id'];
                            $order_goods[$i]['app_order_id'] = $vv['app_order_id'];
                            $order_goods[$i]['chain_id'] = $vv['chain_id'];

                            $order_goods[$i]['sku'] = $vv['sku'];
                            $order_goods[$i]['oc_id'] = $vv['oc_id'];
                            $order_goods[$i]['is_live'] = $vv['is_live'];
                            $i++;
                        }
                    }
                    $goods_insert = $model_order->addOrderGoods($order_goods);

                    if (!empty($goods_insert)) {
                        $insert_id = $insert;
                    }
                    if (empty($goods_insert)) {
                        throw new Exception('订单保存失败[未生成商品数据02]');
                    }
                    if ($insert_id) {
                        $data = array();
                        $data['order_demolition'] = 1; //'拆单状态 默认0，1为拆单'
                        $data['delete_state'] = 1; //放入回收站
                        $data['order_state'] = 0; //父订单更新为已取消状态
                        $condition = array();
                        $condition['order_id'] = $order_id;
                        $update = $model_order->editOrder($data, $condition);
                        if (!$update) {
                            throw new Exception('订单更新失败[保存失败]');
                        }
                    }
                }
            }
        }
    }

    /**
     * 组合子商品价格均摊
     */
    public function buyRecalculateSubGoodsPrice(array &$storeCartLists)
    {
        // 遍历店铺
        foreach ($storeCartLists as &$cartLists) {
            // 遍历商品
            foreach ($cartLists as &$cart) {
                Logic('goods')->recalculateSubGoodsPrice($cart);
            }
        }
    }

    /**
     * 虚拟库存扣减
     *
     * @param $goodsid
     * @param $num
     * @return bool
     */
    public function virtualStockDecrBy($goodsid, $num, $store_id = 0)
    {
        $store_id = $store_id ?: $_SESSION['store_id'] ?: $_REQUEST['store_id'] ?: 1;
        $decr = Redis::decrBy('virtual_stock:' . $goodsid . ':' . $store_id, intval($num));
        // 即使不足这里也不加回，等失败统一处理数据回滚
        $this->virtualStockDecrBy[] = [
            'goods_id' => $goodsid,
            'num' => $num,
            'current' => $decr,
            'store_id' => $store_id,
        ];
        return $decr >= 0;
    }

    /**
     * 同步虚拟库存
     *
     * @param $success
     * @return void
     */
    public function syncVirtualStock($success = false)
    {
        foreach ($this->virtualStockDecrBy as $decr) {
            if ($success) {
                Model()->table('goods')->where(['goods_id' => $decr['goods_id'], 'store_id' => $decr['store_id']])->update([
                    'goods_virtual_storage' => $decr['current']
                ]);
            } else {
                Redis::incrBy('virtual_stock:' . $decr['goods_id'] . ':' . $decr['store_id'], intval($decr['num']));
            }
        }
    }

    /**
     * 百林康源-扫码下单
     * @param array $post
     * @param int $member_id
     * @param string $member_name
     * @param string $member_email
     * @return array
     */
    public function buyStep($post, $member)
    {
        $this->_member_info = $member;
        $member_id = $member['member_id'];
        $this->_post_data = $post;

        $lock = Redis::lock('order:buyStep:' . $member_id, 10)->setAutoRelease();
        if (!$lock->get()) {
            log_error('订单处理中，请不要重复提交,member_id:' . $member_id);
            throw new Exception('订单处理中，请不要重复提交');
        }
        $model = Model('order');
        try {
            $this->_order_data['goods_num'] = 1;
            $swlmArray = [];
            // 如果是深圳利都的箱码
            $len = strlen($this->_post_data['code']);
            if ($len == 10) {
                // 判断箱码是否有效
                $res = SyncUnUbarcode::handleScanDistribution($this->_post_data['code']);
                if ($res['status'] != 200) {
                    throw new Exception($res['message']);
                }

                $res = SyncOutboundLog::GetList($this->_post_data['code']);
                if (!$res) {
                    throw new Exception('无效的箱码');
                }
                $swlmArray = array_column($res, 'barcode');
                $unusedCount = XlogisticsDetail::getUnusedCount($swlmArray);
                $this->_order_data['goods_num'] = $unusedCount;
                foreach ($res as $item) {
                    $model->beginTransaction();
                    $this->_post_data['logistics_code'] = $item['barcode'];
                    $this->_createOrder();
                    $model->commit();
                    $this->blkyDisGoodsAsyncUpdate();
                }
                // 整箱下单，箱码增加到无效记录表
                if (!SyncUnUbarcode::addUnusedBarcode($this->_post_data['code'], 1)) {
                    log_info('整箱下单，箱码增加到无效记录表失败', ['code' => $this->_post_data['code']]);
                }
            } else {
                $model->beginTransaction();
                if ($this->_post_data['code']) {
                    $this->_post_data['logistics_code'] = $this->_post_data['code'];
                }

                $this->_createOrder();

                $model->commit();
                $this->blkyDisGoodsAsyncUpdate();
            }
            return callback(true, '', $this->_order_data);
        } catch (Exception $e) {
            $model->rollback();
            if ($e instanceof SubmitStockException) {
                output_data($e->getStockData());
            }

            return callback(false, $e->getMessage());
        }
    }

    // 百林康源-扫码下单-异步更新商品销量、拆单、下单数量和金额统计
    private function blkyDisGoodsAsyncUpdate()
    {
        //异步更新商品销量
        $goods_data = [
            'goods_id' => $this->_order_data['goods_id'],
            'goods_commonid' => $this->_order_data['goods_commonid'],
            'quantity' => 1,
            'store_id' => $this->_order_data['store_id'],
        ];
        SyncUpdateGoodsStorageQueue::dispatch($goods_data);

        //异步拆单子订单数据
        $order_push = [
            'order_sn' => $this->_order_data['order_sn'],
            'old_order_sn' => $this->_order_data['old_order_sn'],
        ];
        SyncCreateOrderQueue::dispatch($order_push);

        //异步更新下单数量和金额统计
        $dis_member_info = [
            'store_id' => $this->_order_data['store_id'],
            'member_id' => $this->_order_data['member_id'],
            'order_amount' => $this->_order_data['order_amount'],
            'type' => 3,
            'shop_id' => $this->_order_data['shop_id'],
        ];
        SyncDisDistributorFansOrder::dispatch($dis_member_info);

        //如果是贵族的11位物流码，则判断是否拆箱扫码，是则反箱码增加到无效记录表
        if (strlen($this->_post_data['code']) == 11) {
            SyncUnUbarcode::checkUnusedBarcode($this->_post_data['code']);
        }
    }

    private function _createOrder()
    {
        $logPrefix = "宠利扫码下单,物流码或箱码:" . $this->_post_data['logistics_code'];
        log_info($logPrefix, ['member_info' => $this->_member_info]);

        //1 获取参数信息
        $post = $this->_post_data;
        $store_id = intval($this->_post_data['store_id']);
        $member_id = $this->_member_info['member_id'];
        $scrm_user_id = $this->_member_info['scrm_user_id'];
        $member_name = $this->_member_info['member_name'];
        $member_email = $this->_member_info['member_email'];
        $order_member_mobile = $this->_member_info['member_mobile'];
        $rc4_mobile = base64_encode(rc4($order_member_mobile));
        $star_mobile = mobile_star($order_member_mobile);
        $order_from = $this->_post_data['order_from'];
        // 2.1 用户分销员是否认证 dis_distributor
        $dis_member_info = DisDistributor::getDistributor(['member_id' => $member_id, 'org_id' => $store_id]);
        if (empty($dis_member_info) || $dis_member_info['approve_state'] != 2 || $dis_member_info['status'] != 1) {
            throw (new SubmitStockException('基础信息认证审核中'))->setStockData([
                'state' => 401,
                'data' => [
                    'message' => '尚未完成基础认证，请先完成认证'
                ]
            ]);
        }
        $shop_id = $dis_member_info['shop_id'];
        log_info($logPrefix, ['dis_member_info' => $dis_member_info]);
        $dis_commis_rate = 0; //分销佣金比例
        $customer_channel_id = 0; //分销员销售渠道id
        $in_system = 0;
        $org_type = 2;
        // 判断扫的码的长度：百林康源（科学喵商品）的物流码是12位， 箱码是10位；深圳利都（贵族商品）的物流码是11位，箱码是10位
        $goodsType = "guizu"; // 贵族商品
        if (strlen($post['logistics_code']) == 12) {
            $goodsType = "kexuemiao";
            $org_type = 1;
        }

        // 科学喵商品 ， 获取分销佣金
        $cpbh = ""; // 商品编号
        if ($goodsType == "kexuemiao") {
            $res = Xkucun::checkSwlm($post['logistics_code']);
            log_info($logPrefix . '科学喵物流码相关信息', ['data' => json_encode($res)]);
            if ($res['status'] == 200) {
                $cpbh = $res['data']['scpbh'];
            }
        } else {
            // 贵族 11位物流码 获取相关信息
            $res = SyncOutboundLog::CheckCode($post['logistics_code']);
            log_info($logPrefix . '贵族商品物流码或箱码相关信息', ['data' => json_encode($res)]);
            if ($res['status'] == 200) {
                $cpbh = $res['data']['p_no'];
            }
        }

        if ($cpbh) {
            $goods_info = Goods::getGoodsInfo(['goods_serial' => $cpbh, 'store_id' => $store_id]);
        }
        if ($res['status'] == 1) {
            throw new Exception('物流码无效');
        } elseif ($res['status'] == 2) {
            // 贵族商品 箱码 里的某些商品被分销过， 则跳过
            if ($goodsType == "guizu" && strlen($post['code']) == 10) {
                return;
            } else {
                throw (new SubmitStockException('该商品已被已被其他人分销'))->setStockData([
                    'state' => 400,
                    'data' => ['goods_name' => $goods_info['goods_name']]
                ]);
            }
        } elseif ($res['status'] == 3) {
            throw (new SubmitStockException('该商品暂时未产生销售出库记录，无法记录分销佣金'))->setStockData([
                'state' => 4006,
                'data' => ['goods_name' => $goods_info['goods_name']]
            ]);
        } else if ($res['status'] == 4) {
            throw new Exception('商品编号不存在');
        } else if ($res['status'] == 5) {
            throw new Exception('商品已退货，不支持扫码');
        }

        if (empty($goods_info)) {
            throw new Exception('商品信息不存在');
        }
        if ($goods_info['goods_state'] != 1 || $goods_info['goods_verify'] != 1) {
            throw new Exception('商品已下架，无法参与分销活动');
        }
        if (!$goods_info['is_dis']) {
            throw new Exception('该商品不是分销商品，无法参与分销活动');
        }

        // 如果商品不是分销商品， 则直接跳转到操作数据
        if (!$goods_info['is_dis']) {
            goto operate;
        }

        // 先取商品的默认佣金
        $dis_commis_rate = $goods_info['dis_commis_rate'];
        if ($goodsType == "kexuemiao") { // 扫码科学喵商品物流码
            //类型   1：体系内   0：体系外(订单、医生只有有一个是体系内，均认定扫码不得佣金)
            $in_system = 0;
            // 判断出库订单号是否包含前缀XSCK（ 如果是体系内订单，则不论医生是否体系内外身份，均认定扫码不得佣金。体系内订单判断规则：根据订单前缀编号XSCK，只要为这个编号均是体系内订单）
            if (!empty($res['data']['sddbh']) && strpos($res['data']['sddbh'], "XSCK") !== false && strpos($res['data']['sddbh'], "XSCK") == 0) {
                log_info($logPrefix . '物流码相关信息-是体系内的订单', ['member_info' => $this->_member_info, 'data' => json_encode($res)]);
                $in_system = 1;
            }

            //如果是体系外的订单，则判断员工是否体系内的
            if ($in_system == 0 && $dis_member_info['in_system'] == 1) {
                $in_system = 1;
            }

            if ($in_system == 1) {
                $dis_commis_rate = 0;
                $this->_order_data['dis_commis_rate'] = 0; //返回给前端的佣金
            } else {
                if ($res['data'] && $res['data']['dis_commis_rate'] !== null) {
                    $dis_commis_rate = $res['data']['dis_commis_rate'];
                }
                $this->_order_data['dis_commis_rate'] = number_format($dis_commis_rate * $goods_info['goods_price'] / 100, 2);
            }
        } else { // 扫码贵族商品物流码
            // 贵族的 如果特殊物流码配置佣金比例， 则直接用特殊物流码配置佣金比例， 没有设置特殊物流码配置佣金比例， 则进行分销佣金计算
            if ($res['data'] && $res['data']['dis_commis_rate'] !== null) {
                $dis_commis_rate = $res['data']['dis_commis_rate'];
                goto operate;
            }

            // 根据扫码人认证的社会信用编码查询R1库中的客户编码,如果不存在，则佣金为0
            // 查询社会统一信用码
            $enterprise_info = Shop::getShopEnterpriseInfo($dis_member_info['shop_id']);
            if (!$enterprise_info) {
                throw new Exception('店铺信息异常');
            }
            if (!$enterprise_info['social_credit_code']) {
                throw new Exception('认证信息异常:社会统一信用编码不存在');
            }
            $scrmEnterprise  = ScrmEnterprise::GetScrmEnterprise($enterprise_info['social_credit_code']);
            log_info($logPrefix . '根据社会统一信用编码查询R1库中的客户编码', ['scrmEnterprise' => json_encode($scrmEnterprise)]);
            if (!$scrmEnterprise) {
                log_info($logPrefix . '不存在R1客户编码,佣金为0');
                $dis_commis_rate = 0;
                goto operate;
            }

            // 根据R1出库单号 查询出库单信息
            $warehouseSalesOutstock = WarehouseSalesOutstock::GetWarehouseSalesOutstock($res['data']['bill_no']);
            log_info($logPrefix . '根据R1出库单号查询出库单信息', ['warehouseSalesOutstock' => json_encode($warehouseSalesOutstock)]);

            if (!$warehouseSalesOutstock) {
                throw new Exception('出库订单不存在');
            }
            if (!$warehouseSalesOutstock['customer_channel']) {
                throw new Exception('出库订单不存在客户销售渠道');
            }
            $level = 2;
            if ($warehouseSalesOutstock['customer_code'] == $scrmEnterprise['code']) {
                // 一级渠道
                $level = 1;
            }
            // 找出指定的分销佣金
            $dis_customer_channel = DisCustomerChannel::getCustomerChannel($level, $scrmEnterprise['marketing_channel']);
            log_info($logPrefix . '分销员一级销售渠道打标：分销佣金', ['dis_customer_channel' => $dis_customer_channel]);

            if ($dis_customer_channel) {
                $customer_channel_id = $dis_customer_channel['id'];
            }

            if ($dis_customer_channel && $dis_customer_channel['is_del'] == 0 && $dis_customer_channel['dis_commis_setting_id'] && $dis_customer_channel['dis_commis_rate']) {
                $dis_commis_rate = $dis_customer_channel['dis_commis_rate'];
            }
        }

        //开始操作数据
        operate:

        $model_order = Model('order');
        $order = array();
        $order_common = array();
        $order_goods = array();

        //生成支付单号
        $pay_sn = $this->_logic_buy_1->makePaySn($member_id);
        $order_pay = array();
        $order_pay['pay_sn'] = $pay_sn;
        $order_pay['buyer_id'] = $member_id;
        $order_pay_id = $model_order->addOrderPay($order_pay);
        if (!$order_pay_id) {
            log_error('订单保存失败[未生成支付单],member_id:' . $member_id);
            throw new Exception('订单保存失败');
        }
        $old_order_sn = $this->_logic_buy_1->makeOrderSn($order_pay_id);
        $order['order_sn'] = $old_order_sn;
        $order['pay_sn'] = $pay_sn;
        $order['store_id'] = $store_id;
        $order['store_name'] = $goods_info['store_name'];
        $order['buyer_id'] = $member_id;
        $order['buyer_name'] = $member_name;
        $order['buyer_email'] = $member_email;
        $order['encrypt_mobile'] = $rc4_mobile;
        $order['buyer_phone'] = $star_mobile;
        $order['add_time'] = TIMESTAMP;
        $order['payment_code'] = 'offline';
        $order['order_state'] = 10;
        $order['order_amount'] = $goods_info['goods_price'];
        $order['goods_amount'] = $goods_info['goods_price'];
        $order['order_type'] = 1;
        $order['order_from'] = $order_from;
        $order['chain_id'] = 0;
        $order['rpt_amount'] = 0;
        $order['dis_type'] = 2;
        $order['is_dis'] = 1;
        $order['logistics_code'] = $post['logistics_code'];
        $order['in_system'] = $in_system;
        $order['customer_channel_id'] = $customer_channel_id;
        $order['org_type'] = $org_type;
        $order['swlm_status'] = 1;
        $order_id = $model_order->addOrder($order);
        if (!$order_id) {
            log_error('订单保存失败[未生成订单数据],member_id:' . $member_id);
            throw new Exception('订单保存失败');
        }

        $order_common['order_id'] = $order_id;
        $order_common['store_id'] = $store_id;
        $order_common['order_message'] = '';
        $order_common['voucher_price'] = 0;
        $order_common['voucher_code'] = '';
        $order_common['reciver_info'] = '';
        $order_common['reciver_name'] = '';
        $order_common['promotion_info'] =  '';
        $order_common['reciver_date_msg'] = '';
        $insert = $model_order->addOrderCommon($order_common);
        if (!$insert) {
            log_error('订单保存失败[未生成订单扩展数据],member_id:' . $member_id);
            throw new Exception('订单保存失败');
        }

        //保存订单商品
        $order_goods['order_id'] = $order_id;
        $order_goods['goods_id'] = $goods_info['goods_id'];
        $order_goods['goods_name'] = $goods_info['goods_name'];
        $order_goods['goods_price'] = $goods_info['goods_price'];
        $order_goods['goods_num'] = 1;
        $order_goods['goods_image'] = $goods_info['goods_image'];
        $order_goods['goods_pay_price'] = $goods_info['goods_price'];
        $order_goods['store_id'] = $store_id;
        $order_goods['buyer_id'] = $member_id;
        $order_goods['goods_type'] = $goods_info['goods_type'];
        $order_goods['commis_rate'] = 200;
        $order_goods['gc_id'] = $goods_info['gc_id'];
        $order_goods['goods_spec'] = $goods_info['goods_spec'];
        $order_goods['goods_commonid'] = $goods_info['goods_commonid'];
        $order_goods['add_time'] = TIMESTAMP;
        $order_goods['is_dis'] = $goods_info['is_dis'];
        $order_goods['dis_commis_rate'] = $dis_commis_rate;
        $order_goods['dis_member_id'] = $member_id;
        $order_goods['shop_id'] = $shop_id;
        $goods_insert = $model_order->addOrderGoods([$order_goods]);
        if (!$goods_insert) {
            log_error('订单保存失败[未生成商品数据],member_id:' . $member_id);
            throw new Exception('订单保存失败');
        }
        //添加订单日志
        $log_data = array();
        $log_data['order_id'] = $order_id;
        $log_data['log_role'] = '买家';
        $log_data['log_msg'] = '生成订单';
        $log_data['log_user'] = $member_name;
        $log_data['log_orderstate'] = ORDER_STATE_NEW;
        $log_res = $model_order->addOrderLog($log_data);
        if (!$log_res) {
            log_error('订单保存失败[未生成订单日志],member_id:' . $member_id);
        }

        $order_amount = intval($goods_info['goods_price'] * 100);
        $order_sn = Generate();
        $order_main = array(
            "order_sn" => $order_sn,
            "old_order_sn" => $old_order_sn,
            "order_status" => 0,
            "order_status_child" => 20107,
            "shop_id" => $store_id,
            "shop_name" => $goods_info['store_name'],
            "member_id" => $scrm_user_id,
            "member_name" => $member_name,
            "member_tel" => $star_mobile,
            "en_member_tel" => $rc4_mobile,
            "receiver_mobile" => $star_mobile,
            "en_receiver_mobile" => $rc4_mobile,
            "total" => $order_amount,
            "pay_total" => $order_amount,
            "goods_total" => $order_amount,
            "goods_pay_total" => $order_amount,
            "actual_receive_total" => $order_amount,
            "delivery_type" => 3,
            "logistics_code" => "0000",
            "channel_id" => 5,
            "user_agent" => 3,
            "app_channel" => 1,
            "order_pay_type" => "04",
            "org_id" => $store_id,
        );

        $main_insert = OrderMain::create($order_main);
        if (!$main_insert) {
            log_error('订单保存失败[未生成订单主表],member_id:' . $member_id);
            throw new Exception('订单保存失败[未生成订单主表]');
        }

        $order_detail = array(
            'order_sn' => $order_sn,
            'child_channel_name' => $goods_info['store_name'],
        );

        $detail_insert = OrderDetail::create($order_detail);
        if (!$detail_insert) {
            log_error('订单保存失败[未生成订单详情表],member_id:' . $member_id);
            throw new Exception('订单保存失败[未生成订单详情表]');
        }

        $order_product = array(
            "order_sn" => $order_sn,
            "sku_id" => $goods_info['goods_id'],
            'product_id' => $goods_info['goods_commonid'],
            'third_sku_id' => $goods_info['goods_serial'],
            "product_name" => $goods_info['goods_name'],
            "marking_price" => $goods_info['goods_marketprice'] * 100,
            "discount_price" => $order_amount,
            "pay_price" => $order_amount,
            "number" => 1,
            "payment_total" => $order_amount,
            "sku_pay_total" => $order_amount,
            "image" => $goods_info['goods_image'],
            'children_sku' => ''
        );
        $product_insert = OrderProduct::create($order_product);
        if (!$product_insert) {
            log_error('订单保存失败[未生成订单商品表],member_id:' . $member_id);
            throw new Exception('订单保存失败[未生成订单商品表]');
        }
        if ($goodsType == "kexuemiao") {
            //添加XkucunDetail
            $res = Upet\Models\Blky\XkucunDetail::addXkucunDetail(['swlm' => $post['logistics_code']]);
        } else {
            //添加XlogisticsDetail
            $res = Upet\Models\Blky\XlogisticsDetail::addXlogisticsDetail(['swlm' => $post['logistics_code']]);
        }

        if ($res['status'] != 200) {
            log_error('订单保存失败[未更新物流码状态]', ['member_id' => $member_id, 'logistics_code' => $post['logistics_code']]);
            throw new Exception('订单保存失败[未更新物流码状态]');
        }

        //返回前端数据
        $this->_order_data['goods_name'] = $goods_info['goods_name'];
        $this->_order_data['goods_id'] = $goods_info['goods_id'];
        $this->_order_data['goods_commonid'] = $goods_info['goods_commonid'];
        $this->_order_data['quantity'] = 1;
        $this->_order_data['store_id'] = $store_id;
        $this->_order_data['order_sn'] = $order_sn;
        $this->_order_data['old_order_sn'] = $old_order_sn;
        $this->_order_data['member_id'] = $member_id;
        $this->_order_data['dis_commis_rate'] = $dis_commis_rate;
        $this->_order_data['order_amount'] = $order_amount;
        $this->_order_data['shop_id'] = $shop_id;


        // //异步更新商品销量
        // $goods_data = [
        //     'goods_id' => $goods_info['goods_id'],
        //     'goods_commonid' => $goods_info['goods_commonid'],
        //     'quantity' => 1,
        //     'store_id' => $store_id,
        // ];
        // SyncUpdateGoodsStorageQueue::dispatch($goods_data);

        // //异步拆单子订单数据
        // $order_push = [
        //     'order_sn' => $order_sn,
        //     'old_order_sn' => $old_order_sn,
        // ];
        // SyncCreateOrderQueue::dispatch($order_push);

        // //异步更新下单数量和金额统计
        // $dis_member_info = [
        //     'store_id' => $store_id,
        //     'member_id' => $member_id,
        //     'order_amount' => $order_amount,
        //     'type' => 3,
        //     'shop_id' => $shop_id,
        // ];
        // SyncDisDistributorFansOrder::dispatch($dis_member_info);
    }
}
