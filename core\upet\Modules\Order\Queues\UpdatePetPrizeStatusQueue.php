<?php

namespace Upet\Modules\Order\Queues;

use Upet\Integrates\Queue\Dispatchable;
use Upet\Integrates\Queue\Queue;
use Upet\Models\Eshop\PetPrize;

class UpdatePetPrizeStatusQueue implements Queue
{
    use Dispatchable;

    private $voucherCode;

    /**
     * 任务重试次数
     *
     * @var int
     */
    public $tries = 3;

    public function __construct($voucherCode)
    {
        $this->voucherCode = $voucherCode;
    }

    /**
     * 处理pet_prize表的核销状态更新
     */
    public function handle()
    {
        try {
            // 检查eshop库的pet_prize表中是否有对应的coupon_code记录
            if (PetPrize::existsByCouponCode($this->voucherCode)) {
                // 更新receive_status=3为已核销
                $result = PetPrize::updateReceiveStatus($this->voucherCode, 3);

                if (!$result) {
                    throw new \Exception('更新pet_prize表receive_status失败');
                }

                // 记录日志
                log_info('pet_prize_update_log', 'Pet prize status updated successfully for voucher_code: ' . $this->voucherCode);
            } else {
                // 记录日志，表示没有找到对应的记录
                dkcache('pet_prize_update_log', 'No pet prize record found for voucher_code: ' . $this->voucherCode);
            }
        } catch (\Exception $e) {
            dkcache('pet_prize_update_log', 'Failed to update pet prize status for voucher_code: ' . $this->voucherCode . ', error: ' . $e->getMessage());
            throw $e;
        }
    }
}
