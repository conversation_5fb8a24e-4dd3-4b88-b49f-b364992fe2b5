<?php
/**
 * 虚拟兑码退款模型
 *
 *
 *
 *
 * @copyright  Copyright (c) 2007-2018 ShopNC Inc. (http://www.shopnc.net)
 * @license    http://www.shopnc.net
 * @link       http://www.shopnc.net
 * @since      File available since Release v1.1
 */

use Shopnc\Tpl;
use Upet\Models\Datacenter\MemberPropertyGuaranteeQuota;
use Upet\Models\Goods;
use Upet\Models\Order as OrderAlias;
use Upet\Modules\Member\Queues\UpdateMemberVipQueue;
use Upet\Modules\Order\Events\MixOrderRefunded;
use Upet\Modules\Order\Actions\ValidateVipOrderRefundableAction;
use Upet\Models\Datacenter\VipCardOrder;

defined('InShopNC') or exit('Access Invalid!');

class vr_refundModel extends Model
{

    public function __construct()
    {
        parent::__construct();
    }

    /**
     * 增加退款
     *
     * @param
     * @return int
     */
    public function addRefund($refund_array, $order = array())
    {
        if (!empty($order) && is_array($order)) {
            $refund_array['order_id']    = $order['order_id'];
            $refund_array['order_sn']    = $order['order_sn'];
            $refund_array['store_id']    = $order['store_id'];
            $refund_array['store_name']  = $order['store_name'];
            $refund_array['buyer_id']    = $order['buyer_id'];
            $refund_array['buyer_name']  = $order['buyer_name'];
            $refund_array['goods_id']    = $order['goods_id'];
            $refund_array['goods_name']  = $order['goods_name'];
            $refund_array['goods_image'] = $order['goods_image'];
            $refund_array['commis_rate'] = $order['commis_rate'];
        }
        $refund_array['refund_sn'] = $refund_array['refund_sn'] ?: $this->getRefundsn($refund_array['store_id']);

        try {
            $this->beginTransaction();
            $refund_id      = $this->table('vr_refund')->insert($refund_array);
            $code_array     = explode(',', $refund_array['code_sn']);
            $model_vr_order = Model('vr_order');
            $model_vr_order->editOrderCode(['refund_lock' => 1], ['order_id' => $order['order_id'], 'vr_code' => ['in', $code_array]]); //退款锁定
            $this->commit();
            return $refund_id;
        } catch (Exception $e) {
            $this->rollback();
            return false;
        }
    }

    /**
     * 平台退款处理
     *
     * @param
     * @return bool
     */
    public function editOrderRefund($refund)
    {
        $refund_id      = $refund['refund_id'];
        $refund_lock    = '0'; //退款锁定状态:0为正常,1为锁定,2为同意
        $model_vr_order = Model('vr_order');
        try {
            $this->beginTransaction();
            $refund_array                  = array();
            $refund_array['admin_time']    = $refund['admin_time'];
            $refund_array['admin_state']   = $refund['admin_state'];
            $refund_array['admin_message'] = $refund['admin_message'];
            $refund_array['refund_amount'] = $refund['refund_amount'];
            $refund_array['admin_user'] = isset($refund['admin_user']) ? $refund['admin_user'] : '';
            if (C('dianyin_pay')) {
                $refund_array['dy_state']          = $refund['dy_state'];
                $refund_array['dy_refund_id']      = $refund['dy_refund_id'];
                $refund_array['dy_transaction_no'] = $refund['dy_transaction_no'];
            }
            //先提交更新状态，避免后面执行失败导致重复退款
            $state = $this->editRefund(array('refund_id' => $refund_id), $refund_array); //更新退款
            $this->commit();

            //重新开启事务
            $this->beginTransaction();
            $order_id    = $refund['order_id'];
            $order       = $model_vr_order->getOrderInfo(array('order_id' => $order_id));

            if ($state && $refund['admin_state'] == '2') { //审核状态:1为待审核,2为同意,3为不同意
                $refund_lock = '2';
                // 订单编号
                $order_amount = $order['order_amount']; //订单金额
                $rcb_amount = $order['rcb_amount']; //充值卡支付金额
                $predeposit_amount = $order_amount - $order['refund_amount'] - $rcb_amount; //可退预存款金额
                $detail_array = array();

                // 198会员商品退款
                if (Goods::isVipGood($order['goods_id'])) {
                    UpdateMemberVipQueue::dispatch($order['buyer_id'], $order['order_sn']);
                }

                $model_predeposit = Model('predeposit');
                if (($rcb_amount > 0) && ($refund['refund_amount'] > $predeposit_amount)) { //退充值卡
                    $log_array                = array();
                    $log_array['member_id']   = $order['buyer_id'];
                    $log_array['member_name'] = $order['buyer_name'];
                    $log_array['order_sn']    = $order['order_sn'];
                    $log_array['amount']      = $refund['refund_amount'];
                    if ($predeposit_amount > 0) {
                        $log_array['amount'] = $refund['refund_amount'] - $predeposit_amount;
                    }
                    $detail_array['rcb_amount'] = $log_array['amount'];
                    if ($refund['admin_state_virbalance'] == '2') {
                        $state = $model_predeposit->changeRcb('refund', $log_array); //增加买家可用充值卡金额
                    }
                }
                if ($predeposit_amount > 0) { //退预存款
                    $log_array                = array();
                    $log_array['member_id']   = $order['buyer_id'];
                    $log_array['member_name'] = $order['buyer_name'];
                    $log_array['order_sn']    = $order['order_sn'];
                    $log_array['amount']      = $refund['refund_amount']; //退预存款金额
                    if ($refund['refund_amount'] > $predeposit_amount) {
                        $log_array['amount'] = $predeposit_amount;
                    }
                    $pay_amount = floatval($refund['pay_amount']); //已完成在线退款金额
                    if ($pay_amount > 0) {
                        $log_array['amount'] -= $pay_amount;
                    }
                    if ($log_array['amount'] > 0) {
                        $detail_array['pd_amount'] = $log_array['amount'];
                        if ($refund['admin_state_virbalance'] == '2') {
                            $state = $model_predeposit->changePd('refund', $log_array); //增加买家可用预存款金额
                        } else {
                            $detail_array['pd_amount'] = 0;
                        }
                    }
                }

                if ($state) {
                    //更新退款详细
                    $detail_array['refund_state'] = '2';
                    $detail_array['pay_time'] = time(); //退款完成时间
                    $detail_array['refund_amount'] = $refund['refund_amount'];
                    $this->editDetail(array('refund_id' => $refund_id), $detail_array);

                    //更新订单退款
                    $order_array                  = array();
                    $order_amount                 = $order['order_amount']; //订单金额
                    $refund_amount                = $order['refund_amount'] + $refund['refund_amount']; //退款金额
                    $order_array['refund_state']  = ($order_amount - $refund_amount) > 0 ? 1 : 2;
                    $order_array['refund_amount'] = ncPriceFormat($refund_amount);
                    $state                        = $model_vr_order->editOrder($order_array, array('order_id' => $order_id));

                    //更新赠品订单状态
                    $model_gift_order = Model('gift_order');
                    $gift_order_info  = $model_gift_order->getOrderInfo(['order_id_from' => $order_id], 'order_id,order_state');
                    if (is_array($gift_order_info) && !empty($gift_order_info)) {
                        $toubao_starttime = date('Y-m-d', ($order['payment_time'] + 1 * 24 * 60 * 60));
                        $toubao_starttime = strtotime($toubao_starttime);
                        $toubao_endtime   = $toubao_starttime + 7 * 24 * 60 * 60;
                        $now_time         = time();
                        if ($toubao_endtime > $now_time) { //7天之内
                            $model_gift_order->editOrder(['order_state' => 0, 'refund_state' => $order_array['refund_state'], 'close_time' => $refund_array['admin_time'], 'close_reason' => '商品订单已申请退款'], ['order_id' => $gift_order_info['order_id']]);
                        }
                    }

                    //vip-2.0.1 退单增加服务金，购买商品时， 使用了服务金，发生退款时，返还用户服务金
                    if ($order['order_promotion_type'] == 17 && $ps = unserialize($order['order_common'])) {
                        foreach ($ps as $p) {
                            if (empty($p[2]['type']) || $p[2]['type'] != 'insurance') {
                                continue;
                            }
                            //兼容历史订单
                            if (isset($p[2]['insurance_policy_number']) &&  $p[2]['insurance_policy_number']) {
                                $deductedRecords = [[
                                    'insurance_policy_number' => $p[2]['insurance_policy_number'],
                                    'discount' => floor($p[1] / $order['goods_num'] * $refund['goods_num'] * 100 + 0.0001) / 100
                                ]];
                            } else {
                                //退款时则倒序退服务金
                                if (isset($p[2]['extras']['deductedRecords'])) {
                                    $deductedRecords = array_reverse($p[2]['extras']['deductedRecords']);
                                } else if (isset($p[2]['extras'][0]['deductedRecords'])) {
                                    $deductedRecords = array_reverse($p[2]['extras'][0]['deductedRecords']);
                                }
                            }
                            $refundIncr = [
                                'order_id' => 'v-' . $order['order_id'], // v-23,r-23 子订单id，跳转详情使用
                                'refund_amount' => floor($p[1] / $order['goods_num'] * $refund['goods_num'] * 100 + 0.0001) / 100, // 实付金额
                                'goods_name' => $order['goods_name'],
                                'goods_id' => $order['goods_id'],
                                'buyer_id' => $order['buyer_id'],
                            ];
                            MemberPropertyGuaranteeQuota::refundIncr($refundIncr, $deductedRecords);
                            break;
                        }
                    }
                    //vip-2.0.1 用户退卡
                    if ($order['order_type'] == 17) {
                        MemberPropertyGuaranteeQuota::VipCardRefund($order['erp_order_sn']);
                    }

                    // 订单退款事件触发
                    event(new MixOrderRefunded($order));

                    // 查看是否使用虚拟库存，有则退还虚拟库存
                    if ($order['is_use_virtual_stock']) {
                        Goods::virtualStockIncrBy($order['goods_id'], intval($order['goods_num']), $order['store_id']);
                    }
                }
            }
            if ($state) {
                $code_array = explode(',', $refund['code_sn']);
                $state      = $model_vr_order->editOrderCode(array('refund_lock' => $refund_lock), array('vr_code' => array('in', $code_array))); //更新退款的兑换码

                if ($state && $refund['admin_state'] == '2') {
                    // 全额退款订单状态取消
                    if (count($code_array) == $order['goods_num']) {
                        Logic('vr_order')->changeOrderStateCancel($order, 'system', '商品全部退款完成取消订单', false, false);
                    } else {
                        Logic('vr_order')->changeOrderStateSuccess($order_id); //更新订单状态
                    }
                }
            }
            $this->commit();
            return $state;
        } catch (Exception $e) {
            $this->rollback();
            return false;
        }
    }

    /**
     * 增加退款详细
     *
     * @param
     * @return int
     */
    public function addDetail($refund, $order, $BASE_PATH = "")
    {
        $detail_array                  = array();
        $detail_array['refund_id']     = $refund['refund_id'];
        $detail_array['order_id']      = $refund['order_id'];
        $detail_array['batch_no']      = date('YmdHis') . $refund['refund_id']; //批次号。支付宝要求格式为：当天退款日期+流水号。
        $detail_array['refund_amount'] = ncPriceFormat($refund['refund_amount']);
        $detail_array['refund_code']   = 'predeposit';
        $detail_array['refund_state']  = '1';
        $detail_array['add_time']      = time();
        if (!empty($order['trade_no']) && in_array($order['payment_code'], OrderAlias::PAY_CENTER_TYPES)) { //微信支付

            //如果存在传递路径
            if ($BASE_PATH) {
                if (!class_exists('WxPayConfig')) {
                    $api_file = $BASE_PATH . DS . 'api' . DS . 'refund' . DS . 'wxpay' . DS . 'WxPay.Config.php';
                }
            } else {
                $api_file = BASE_PATH . DS . 'api' . DS . 'refund' . DS . 'wxpay' . DS . 'WxPay.Config.php';
            }

            if ($order['payment_code'] == 'wxpay') {
                if ($BASE_PATH) {
                    if (!class_exists('WxPayConfig')) {
                        $api_file = $BASE_PATH . DS . 'api' . DS . 'refund' . DS . 'wxpay' . DS . 'WxPayApp.Config.php';
                    }
                } else {
                    $api_file = BASE_PATH . DS . 'api' . DS . 'refund' . DS . 'wxpay' . DS . 'WxPayApp.Config.php';
                }
            }
            if (!class_exists('WxPayConfig')) {
                include $api_file;
            }
            $apiclient_cert = \WxPayConfig::SSLCERT_PATH;
            $apiclient_key  = \WxPayConfig::SSLKEY_PATH;
            if (!empty($apiclient_cert) && !empty($apiclient_key)) { //验证商户证书路径设置
                $detail_array['refund_code'] = $order['payment_code'];
            }
        }
        if (!empty($order['trade_no']) && in_array($order['payment_code'], array('alipay', 'ali_native'))) { //支付宝
            $detail_array['refund_code'] = 'alipay';
        }
        $result = $this->table('vr_refund_detail')->insert($detail_array);
        return $result;
    }

    /**
     * 修改退款
     *
     * @param
     * @return bool
     */
    public function editRefund($condition, $data)
    {
        if (empty($condition)) {
            return false;
        }
        if (is_array($data)) {
            $result = $this->table('vr_refund')->where($condition)->update($data);
            return $result;
        } else {
            return false;
        }
    }

    /**
     * 修改退款详细记录
     *
     * @param
     * @return bool
     */
    public function editDetail($condition, $data)
    {
        if (empty($condition)) {
            return false;
        }
        if (is_array($data)) {
            $result = $this->table('vr_refund_detail')->where($condition)->update($data);
            return $result;
        } else {
            return false;
        }
    }

    /**
     * 退款编号
     *
     * @param
     * @return array
     */
    public function getRefundsn($store_id)
    {
        $result = mt_rand(100, 999) . substr(500 + $store_id, -3) . date('ymdHis');
        return $result;
    }

    /**
     * 退款详细记录
     *
     * @param
     * @return array
     */
    public function getDetailInfo($condition = array(), $fields = '*', $master = false)
    {
        return $this->table('vr_refund_detail')->where($condition)->field($fields)->master($master)->find();
    }

    /**
     * 订单在线退款计算
     *
     * @param
     * @return array
     */
    public function getPayDetailInfo($detail_array)
    {
        $condition             = array();
        $condition['order_id'] = $detail_array['order_id'];
        $model_order           = Model('vr_order');
        $order                 = $model_order->getOrderInfo($condition); //订单详细
        $order['pay_amount']   = ncPriceFormat($order['order_amount'] - $order['rcb_amount'] - $order['pd_amount']); //在线支付金额=订单总价格-充值卡支付金额-预存款支付金额
        $out_amount            = bcsub($order['pay_amount'], $order['refund_amount'], 2); //可在线退款金额

        $refund_amount = $detail_array['refund_amount']; //本次退款总金额
        if ($refund_amount > $out_amount) {
            $refund_amount = $out_amount;
        }
        $order['pay_refund_amount'] = ncPriceFormat($refund_amount);
        $condition                  = array();
        $condition['payment_code']  = $order['payment_code'];
        if (in_array($order['payment_code'], array('wxpay', 'wx_jsapi', 'ali_native'))) { //手机客户端支付
            if ($order['payment_code'] == 'wx_jsapi') {
                $condition['payment_code'] = 'wxpay_jsapi';
            }
            if ($order['payment_code'] == 'ali_native') {
                $condition['payment_code'] = 'alipay_native';
            }
            $model_payment  = Model('mb_payment');
            $payment_info   = $model_payment->getMbPaymentInfo($condition); //接口参数
            $payment_info   = $payment_info['payment_config'];
            $payment_config = $payment_info;
            if ($order['payment_code'] == 'wxpay') {
                $payment_config['appid'] = $payment_info['wxpay_appid'];
                $payment_config['mchid'] = $payment_info['wxpay_partnerid'];
                $payment_config['key']   = $payment_info['wxpay_partnerkey'];
            }
            if ($order['payment_code'] == 'wx_jsapi') {
                $payment_config['appid'] = $payment_info['appId'];
                $payment_config['mchid'] = $payment_info['partnerId'];
                $payment_config['key']   = $payment_info['apiKey'];
            }
        } else {
            if ($order['payment_code'] == 'wx_saoma') {
                $condition['payment_code'] = 'wxpay';
            }
            $model_payment  = Model('payment');
            $payment_info   = $model_payment->getPaymentInfo($condition); //接口参数
            $payment_config = unserialize($payment_info['payment_config']);
        }
        $order['payment_config'] = $payment_config;
        return $order;
    }

    /**
     * 单条退款记录
     *
     * @param
     * @return array
     */
    public function getRefundInfo($condition = array(), $fields = '*', $master = false)
    {
        return $this->table('vr_refund')->where($condition)->field($fields)->master($master)->find();
    }

    /**
     * 退款记录
     *
     * @param
     * @return array
     */
    public function getRefundList($condition = array(), $page = '', $limit = '', $fields = '*', $order = 'refund_id desc')
    {
        $result = $this->table('vr_refund')->field($fields)->where($condition)->page($page)->limit($limit)->order($order)->select();
        return $result;
    }

    /**
     * 检查是否使用了vr_order字段查询
     *
     * @return bool
     */
    public function checkParamsUseVrOrderField()
    {
        if (isset($_GET['order_from']) && $_GET['order_from'] > 0) {
            return true;
        }
        if (isset($_GET['qtype_time']) && $_GET['qtype_time'] == 'payment_time') {
            return true;
        }
        return false;
    }

    /**
     * 退款连表查询
     */
    function getRefundJoinOrdersList($condition = array(), $page = '', $order = 'vr_refund.refund_id desc', $limit = '', $field = 'vr_refund.*,vr_order.order_type,vr_order.erp_order_sn')
    {
        return $this->table('vr_refund,vr_order')->join('left')->on('vr_refund.order_id=vr_order.order_id')->field($field)->where($condition)->page($page)->limit($limit)->order($order)->select();;
    }

    /**
     * 退款记录和code
     *
     * @param
     * @return array
     */
    public function getRefundListAndCode($condition = array(), $page = '', $limit = '', $fields = '*', $order = 'refund_id desc')
    {
        $on     = "vr_refund.code_sn=vr_order_code.vr_code";
        $result = $this->table('vr_refund,vr_order_code')->field($fields)->join('left')->on($on)->where($condition)->page($page)->order($order)->limit($limit)->select();
        return $result;
    }

    public function getRefundListAndCodeAndOrder($condition = array(), $page = '', $limit = '', $fields = '*', $order = 'vr_refund.refund_id desc')
    {
        $on     = "vr_refund.code_sn=vr_order_code.vr_code, vr_order_code.order_id = vr_order.order_id";
        $result = $this->table('vr_refund,vr_order_code,vr_order')->field($fields)->join('left,left')->on($on)->where($condition)->page($page)->order($order)->limit($limit)->select();
        return $result;
    }

    /**
     * 取得退款记录的数量
     * @param array $condition
     */
    public function getRefundCount($condition)
    {
        if ($this->checkParamsUseVrOrderField()) {
            return $this->table('vr_refund,vr_order')->join('left')->on('vr_refund.order_id=vr_order.order_id')->where($condition)->count();
        }
        return $this->table('vr_refund')->where($condition)->count();
    }

    /**
     * 详细页右侧订单信息
     *
     * @param
     * @return array
     */
    public function getRightOrderList($order_condition)
    {
        $order_id       = $order_condition['order_id'];
        $model_vr_order = Model('vr_order');
        $order_info     = $model_vr_order->getOrderInfo($order_condition, "*", true); //查找主库，防止延迟
        Tpl::output('order', $order_info);
        $order_list            = array();
        $order_list[$order_id] = $order_info;
        $order_list            = $model_vr_order->getCodeRefundList($order_list); //没有使用的兑换码列表
        $order_info            = $order_list[$order_id];
        $model_store           = Model('store');
        $store                 = $model_store->getStoreInfo(array('store_id' => $order_info['store_id']));
        Tpl::output('store', $store);

        //显示退款
        $order_info['if_refund'] = $model_vr_order->getOrderOperateState('refund', $order_info);

        if ($order_info['if_refund']) {
            $code_list = $order_info['code_list'];
            Tpl::output('code_list', $code_list);
        }
        return $order_info;
    }

    /*
     *  获得退款的店铺列表
     *  @param array $complain_list
     *  @return array
     */
    public function getRefundStoreList($list)
    {
        $store_ids = array();
        if (!empty($list) && is_array($list)) {
            foreach ($list as $key => $value) {
                $store_ids[] = $value['store_id']; //店铺编号
            }
        }
        $field = 'store_id,store_name,member_id,member_name,seller_name,store_company_name,store_qq,store_ww,store_phone,store_domain';
        return Model('store')->getStoreMemberIDList($store_ids, $field);
    }

    /**
     * 向模板页面输出退款状态
     *
     * @param
     * @return array
     */
    public function getRefundStateArray($type = 'all')
    {
        $admin_array = array(
            '1' => '待审核',
            '2' => '同意',
            '3' => '不同意',
            '4' => '已取消',
        ); //退款状态:1为待审核,2为同意,3为不同意
        Tpl::output('admin_array', $admin_array);

        $state_data = array(
            'admin' => $admin_array
        );
        if ($type == 'all') return $state_data; //返回所有
        return $state_data[$type];
    }

    public function vrRefundApply($data)
    {
        $condition = array();
        $condition['buyer_id'] = $data['member_id'];
        $condition['order_id'] = $data['order_id'];
        $order = $this->getRightOrderList($condition);
        $order_id = $order['order_id'];
        if (!$order['if_refund'] && !in_array($order['order_type'], [17, 18])) {
            output_error('订单退款审核中');
        }

        $code_list = $order['code_list'];
        $refund_array = array();
        $goods_num = 0; //兑换码数量
        $refund_amount = 0; //退款金额
        $code_sn = '';
        $rec_id_array = $code_list;
        if (in_array($order['order_type'], [17, 18])) {
            $condition = array();
            $condition['order_id'] = $order_id;
            $condition['refund_lock'] = '0'; //退款锁定状态:0为正常(能退款),1为锁定(待审核),2为同意
            $code_list = Model('vr_order')->getCodeList($condition, "*", "", "rec_id desc", "", "", true);
            $rec_id_array = $code_list;
            if (!empty($rec_id_array) && is_array($rec_id_array)) { //选择退款的兑换码
                foreach ($rec_id_array as $key => $value) {
                    $code = $code_list[$key];
                    if ((!empty($code) && $code['vr_state'] != '1') || in_array($order['order_type'], [17, 18])) {
                        $goods_num += 1;
                        $refund_amount += $code['pay_price']; //实际支付金额
                        $code_sn .= $code['vr_code'] . ','; //兑换码编号
                    }
                }
            }
        } else {
            if (!empty($rec_id_array) && is_array($rec_id_array)) { //选择退款的兑换码
                foreach ($rec_id_array as $key => $value) {
                    $code = $code_list[$key];
                    if (!empty($code)) {
                        $goods_num += 1;
                        $refund_amount += $code['pay_price']; //实际支付金额
                        $code_sn .= $code['vr_code'] . ','; //兑换码编号
                    }
                }
            }
        }
        if ($goods_num < 1) {
            output_error('没有可退数量');
        }

        if (Goods::isVipGood($order['goods_id']) && $order['payment_time'] > C('vip_timeline')) {
            try {
                action(new ValidateVipOrderRefundableAction($order));
            } catch (Exception $e) {
                output_error($e->getMessage());
            }

            $model_member = Model('member');
            $refund_amount = $model_member->getMemberVipRefundMoney($refund_amount, $order);
            if ($refund_amount == 0) {
                output_error('没有可退金额,已过会员协议退款期限');
            }
        }
        //有赠品订单
        $model_gift_order = Model('gift_order');
        $gift_order_info = $model_gift_order->getOrderInfo(['order_id_from' => $order_id], 'order_id,goods_price,order_state,payment_time');
        if (is_array($gift_order_info) && !empty($gift_order_info)) {
            $toubao_starttime = date('Y-m-d', ($order['payment_time'] + 1 * 24 * 60 * 60));
            $toubao_starttime = strtotime($toubao_starttime);
            $toubao_endtime = $toubao_starttime + 7 * 24 * 60 * 60;
            $now_time = time();
            if ($toubao_endtime < $now_time && $gift_order_info['order_state'] == ORDER_STATE_SUCCESS) {
                $refund_amount = $refund_amount - ncPriceFormat($gift_order_info['goods_price']);
            }
        }
        $refund_array['refund_sn'] = $this->getRefundsn($order['store_id']);
        $refund_array['code_sn'] = rtrim($code_sn, ',');
        $refund_array['admin_state'] = '1'; //状态:1为待审核,2为同意,3为不同意
        $refund_array['refund_amount'] = $data['apply_type'] == 1 ? 0 : ncPriceFormat($refund_amount);
        $refund_array['goods_num'] = $goods_num;
        $refund_array['buyer_message'] = $data['buyer_message'];
        $refund_array['add_time'] = time();
        $refund_array['order_id'] = $order_id;
        $refund_array['apply_type'] = isset($data['apply_type']) ? $data['apply_type'] : 2;
        $refund_array['apply_user'] = isset($data['apply_user']) ? $data['apply_user'] : 0;

        //虚拟订单申请售后  同步ERP数据中心
        /** @var refundLogic $refundLogic */
        $refundLogic = Logic('refund');
        $refundapply = $refundLogic->refundVrOrderApply($refund_array);
        if (!$refundapply) {
            output_error('冻结ERP退款订单失败');
        }
        $state = $this->addRefund($refund_array, $order);
        if ($state) {
            //添加退款明细
            $refund_array['refund_id'] = $state;
            $this->addDetail($refund_array, $order);
            output_data("1", array('create_time' => str_pad($order['add_time'], 13, "0", STR_PAD_RIGHT)));
        } else {
            output_error('退款申请保存失败');
        }
    }
}
